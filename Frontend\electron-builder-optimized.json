{"appId": "com.aivision.app", "productName": "AI Vision App", "directories": {"output": "release", "buildResources": "build"}, "files": ["dist/**/*", "electron/**/*", "package.json", "node_modules/**/*"], "extraResources": [{"from": "../Backend_Django", "to": "Backend_Django", "filter": ["**/*", "!**/__pycache__/**", "!**/*.pyc", "!**/.git/**", "!**/venv/**", "!**/venv_electron/**", "!**/node_modules/**", "!**/.pytest_cache/**", "!**/test_*.py", "!**/tests/**", "!**/db.sqlite3"]}, {"from": "../data/models/system_models", "to": "data/models/system_models", "filter": ["**/*"]}, {"from": "python-portable", "to": "python", "filter": ["**/*"]}], "win": {"icon": "public/favicon.ico", "target": [{"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AI Vision App", "installerIcon": "public/favicon.ico", "uninstallerIcon": "public/favicon.ico", "installerHeaderIcon": "public/favicon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "perMachine": false, "allowElevation": true}, "mac": {"icon": "public/favicon.ico", "target": ["dmg"], "category": "public.app-category.developer-tools"}, "linux": {"icon": "public/favicon.ico", "target": ["AppImage"], "category": "Development"}, "compression": "normal", "npmRebuild": false, "nodeGypRebuild": false, "buildDependenciesFromSource": false}