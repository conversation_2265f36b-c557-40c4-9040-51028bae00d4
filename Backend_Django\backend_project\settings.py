"""
Django settings for backend_project project.

Generated by 'django-admin startproject' using Django 5.2.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-q!qvev@pe__qs170saztzqy%^6msnq11&@yt(_@-!%(mc=($h-"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['*************', '*'] # 允许开发机IP和所有主机访问


# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders", # 添加 corsheaders
    "vision_app",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware", # 添加 corsheaders 中间件
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "backend_project.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "backend_project.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db" / "db.sqlite3",
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "static/"

# Media files (User-uploaded files)
# https://docs.djangoproject.com/en/5.2/topics/files/
MEDIA_URL = '/media/' # Django 会将 MEDIA_ROOT 下的文件通过这个 URL 路径提供服务
# 新的自定义模型存储根目录，注意：upload_to 将在此目录下创建子文件夹
MEDIA_ROOT = BASE_DIR / 'models' / 'custom_models'

# 新的系统模型存储根目录
SYSTEM_MODELS_ROOT = BASE_DIR / 'models' / 'system_models'

# 示例图片存储根目录
EXAMPLE_IMAGES_ROOT = BASE_DIR / 'models' / 'example_images'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# CORS settings
# CORS_ALLOW_ALL_ORIGINS = True # 禁用全开放，改用白名单
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://*************:5173",
]
CSRF_TRUSTED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://*************:5173",
]
CORS_ALLOW_CREDENTIALS = True # 允许发送 cookies 和会话信息

# 会话配置
SESSION_COOKIE_AGE = 86400  # 会话有效期：24小时（秒）
SESSION_SAVE_EVERY_REQUEST = True  # 每次请求都保存会话
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # 浏览器关闭时不过期会话
SESSION_COOKIE_SAMESITE = "Lax"  # 本地开发推荐Lax，跨域如需可用None+https
SESSION_COOKIE_SECURE = False     # 本地开发用False，生产https用True

# OCR Task Specific Configurations
OCR_TASK_CONFIGS = {

    'license_plate_cn': { # 车牌识别任务配置
        'use_angle_cls': False, # 是否使用方向分类器
        'det_algorithm': 'DB',     # 检测算法
        'rec_algorithm': 'CRNN',   # 识别算法
        'lang': 'ch', # 语言
        'use_gpu': False, # 默认 False，可在 API 请求中覆盖
        'rec_image_shape': "3,64,320", # 识别模型输入形状
        'det_limit_type': "max", # 检测尺寸限制类型
        'det_limit_side_len': 320, # 检测尺寸限制长度
        'det_db_thresh': 0.01, # DB检测阈值
        'det_db_box_thresh': 0.01, # DB框阈值
        'use_onnx': False, # 是否使用 ONNX 推理
        'use_npu': False, # 是否使用 NPU 推理
        'use_mlu': False, # 是否使用 MLU 推理
        'use_xpu': False, # 是否使用 XPU 推理
        'return_word_box': False, # 是否返回单字框
        # 识别字典文件路径
        'rec_char_dict_path': str(BASE_DIR / 'models' / 'system_models' / 'ocr' / 'car_liencese_ch' / 'AI_OCR_Rec_CHNLP_NCHW_1x3x64x320' / 'ppocr_keys_v1.txt'),
        # PaddleOCRSystemPredictor 中未在此处显式设置的默认参数：
        # det_box_type: "quad", enable_mkldnn: False, cpu_threads: 10, precision: "fp32",
        # benchmark: False, save_log_path: "./log_output/", show_log: True (但会被 Django 日志覆盖),
        # use_tensorrt: False, gpu_id: 0, rec_batch_num: 6,
        # rec_char_dict_path: (由 predictor 根据 paddle_ocr_lib_path 构建),
        # use_space_char: True, vis_font_path: (由 predictor 构建),
        # crop_res_save_dir: "./output", save_crop_res: False, warmup: False,
        # det_db_unclip_ratio: 1.5, det_db_score_mode: "slow", use_dilation: False,
        # cls_image_shape: "3, 48, 192", label_list: ['0', '180'], cls_batch_num: 6, cls_thresh: 0.9
    },

    'id_card_en': { # 证件号码识别任务配置（英文）
        'use_angle_cls': False, # 根据你的模型是否包含分类器调整
        # 'cls_model_dir': str(SYSTEM_MODELS_ROOT / 'ocr' / 'Identity_card_ch' / 'Identity_card_cls_model' / 'inference'), # 如果有分类器模型且使用，添加此行
        'det_algorithm': 'DB',     # 根据你的模型实际使用的算法调整
        'rec_algorithm': 'CRNN',   # 根据你的模型实际使用的算法调整
        'lang': 'ch', # 根据你的模型语言调整
        'use_gpu': False, # 默认 False，可在 API 请求中覆盖
        # 注意：rec_image_shape, det_limit_side_len, det_db_thresh 等参数应根据你的身份证模型特性进行配置
        'rec_image_shape': "3,48,320", # rec_image_shape 是识别模型的输入形状，通常为 "3,48,320"
        'det_limit_side_len': 640,    # det_limit_side_len 是检测模型的输入尺寸限制，通常为 640
        'det_db_thresh': 0.3,        # det_db_thresh 是检测模型的阈值，通常为 0.3
        'det_db_box_thresh': 0.6,    # det_db_box_thresh 是检测模型的框阈值，通常为 0.6
        'det_db_unclip_ratio': 1.5,  # det_db_unclip_ratio 是检测模型的非裁剪比率，通常为 1.5
        'use_space_char': True,      # use_space_char 是是否使用空格符，通常为 True
        # **明确指定身份证识别模型的字典文件路径**
        'rec_char_dict_path': str(BASE_DIR / 'models' / 'system_models' / 'ocr' / 'Identity_card_number_en' / 'Identity_card_rec_model' / 'en_dict.txt'),
        # 其他默认参数将从 ocr_paddle_predictor.py 中的 PaddleOCRSystemPredictor 默认值获取
        'use_onnx': False, # 是否使用 ONNX 推理
        'use_npu': False, # 是否使用 NPU 推理
        'use_mlu': False, # 是否使用 MLU 推理
        'use_xpu': False, # 是否使用 XPU 推理
        'return_word_box': False, # 根据需要调整，通常OCR不需要这个
    },

    'general_text_mobile_ch_en': { # PP-OCRv4 mobile模型配置（中英文）
        'use_angle_cls': False, # 轻量级模型通常不使用方向分类器
        'det_algorithm': 'DB',     # PP-OCRv4使用DB检测算法
        'rec_algorithm': 'SVTR_LCNet', # PP-OCRv4使用SVTR_LCNet识别算法
        'lang': 'ch', # 中文语言
        'use_gpu': False, # 默认CPU推理，适合移动端部署
        'rec_image_shape': "3,48,320", # PP-OCRv4 mobile标准输入尺寸
        'det_limit_side_len': 640,    # 检测模型输入限制
        'det_db_thresh': 0.3,        # DB检测阈值
        'det_db_box_thresh': 0.6,    # DB框阈值
        'det_db_unclip_ratio': 1.5,  # DB非裁剪比率
        'use_space_char': True,      # 使用空格符
        # PP-OCRv4 mobile使用标准中英文字典
        'rec_char_dict_path': str(BASE_DIR / 'models' / 'system_models' / 'ocr' / 'general_ocr_mobile_ch_en' / 'PP-OCRv4_mobile_rec_inference_ch_en' / 'ppocr_keys_v1.txt'),
        'use_onnx': False, # 使用PaddlePaddle推理
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
        # PP-OCRv4 mobile特有参数
        'det_db_score_mode': 'fast', # 快速模式，适合移动端
        'rec_batch_num': 6, # 批处理数量
        'max_text_length': 25, # 最大文本长度
    },

    'general_text_mobile_en': { # PP-OCRv4 mobile模型配置（纯英文）
        'use_angle_cls': False, # 轻量级模型通常不使用方向分类器
        'det_algorithm': 'DB',     # PP-OCRv4使用DB检测算法
        'rec_algorithm': 'SVTR_LCNet', # PP-OCRv4使用SVTR_LCNet识别算法
        'lang': 'en', # 英文语言
        'use_gpu': False, # 默认CPU推理，适合移动端部署
        'rec_image_shape': "3,48,320", # PP-OCRv4 mobile标准输入尺寸
        'det_limit_side_len': 640,    # 检测模型输入限制
        'det_db_thresh': 0.3,        # DB检测阈值
        'det_db_box_thresh': 0.6,    # DB框阈值
        'det_db_unclip_ratio': 1.5,  # DB非裁剪比率
        'use_space_char': True,      # 使用空格符
        # PP-OCRv4 mobile使用英文字典
        'rec_char_dict_path': str(BASE_DIR / 'models' / 'system_models' / 'ocr' / 'general_ocr_mobile_en' / 'PP-OCRv4_mobile_rec_inference_en' / 'en_dict.txt'),
        'use_onnx': False, # 使用PaddlePaddle推理
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
        # PP-OCRv4 mobile特有参数
        'det_db_score_mode': 'fast', # 快速模式，适合移动端
        'rec_batch_num': 6, # 批处理数量
        'max_text_length': 25, # 最大文本长度
    },

    'default': { # Fallback default parameters, can be more generic
        'use_angle_cls': False,
        'det_algorithm': 'DB',
        'rec_algorithm': 'SVTR_LCNet', # A common general purpose algorithm
        'lang': 'ch',
        'use_gpu': False,
        'rec_image_shape': "3,48,320",
        'det_limit_side_len': 960, # Changed from 640 to 960 for a more general default
        'det_db_thresh': 0.3,
        'det_db_box_thresh': 0.6,
        'use_onnx': False,
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
    }
}
