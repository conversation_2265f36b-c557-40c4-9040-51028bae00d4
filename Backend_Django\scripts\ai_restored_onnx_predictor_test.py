import onnxruntime
import numpy as np
from PIL import Image

def preprocess_image(image_path, target_size=(256, 256)):
    """
    加载、预处理输入图片以匹配ONNX模型输入。
    """
    # 1. 读取图片
    img = Image.open(image_path)

    # 2. 转换为灰度图 (因为模型输入通道为1)
    img = img.convert('L')

    # 3. 调整大小
    img = img.resize(target_size, Image.Resampling.LANCZOS) # 或者 Image.BILINEAR

    # 4. 转换为NumPy数组
    img_np = np.array(img, dtype=np.float32)

    # 5. 归一化 (将像素值从 [0, 255] 缩放到 [0.0, 1.0])
    #    如果你的模型训练时使用了不同的归一化方法 (例如，减均值除标准差，或缩放到[-1, 1])，
    #    你需要在这里修改。
    img_np = img_np / 255.0

    # 6. 增加批次和通道维度 (H, W) -> (1, 1, H, W)
    img_np = np.expand_dims(img_np, axis=0)  # (1, H, W)
    img_np = np.expand_dims(img_np, axis=0)  # (1, 1, H, W)

    return img_np.astype(np.float32) # 确保是float32

def postprocess_output(output_tensor):
    """
    后处理模型输出张量以保存为图片。
    """
    # 1. 移除批次和通道维度 (1, 1, H, W) -> (H, W)
    output_image_np = output_tensor.squeeze()

    # 2. 反归一化 (假设输出在 [0.0, 1.0] 范围)
    #    如果模型输出的范围不是 [0.0, 1.0] (例如，可能是logits)，你需要调整这里。
    #    例如，如果输出是激活前的logits，可能需要sigmoid激活后再反归一化。
    #    这里我们先假设输出是归一化后的图像数据。
    output_image_np = np.clip(output_image_np * 255.0, 0, 255) # clip确保值在0-255之间

    # 3. 转换为uint8类型
    output_image_np = output_image_np.astype(np.uint8)

    # 4. 从NumPy数组创建PIL Image对象
    output_image = Image.fromarray(output_image_np, mode='L') # 'L' for grayscale

    return output_image

def main():
    model_path = "../models/system_models/ai_restored/AI_Restorer_NCHW_1x1x256x256_V1.0.1.1.onnx"  # 替换为你的模型路径
    input_image_path = "./modified_Aluminum_Foil_Paper_9.bmp"    # 替换为你的输入图片路径
    output_image_path = "output.png"  # 输出图片的保存路径

    # --- 1. 加载ONNX模型并创建推理会话 ---
    try:
        session = onnxruntime.InferenceSession(model_path, providers=['CPUExecutionProvider'])
        print(f"ONNX model '{model_path}' loaded successfully.")
    except Exception as e:
        print(f"Error loading ONNX model: {e}")
        return

    # --- 2. 获取模型输入输出的名称 ---
    # 你已经提供了名称，但通常也可以从模型中获取
    input_name = session.get_inputs()[0].name
    output_name = session.get_outputs()[0].name
    print(f"Model Input Name: {input_name}")   # 应为 'images'
    print(f"Model Output Name: {output_name}") # 应为 'output0'

    # --- 3. 预处理输入图片 ---
    try:
        input_tensor = preprocess_image(input_image_path)
        print(f"Input image '{input_image_path}' preprocessed. Tensor shape: {input_tensor.shape}, dtype: {input_tensor.dtype}")
    except FileNotFoundError:
        print(f"Error: Input image '{input_image_path}' not found.")
        # 创建一个虚拟的输入图片用于测试
        print("Creating a dummy 512x512 grayscale image as input.png for testing...")
        try:
            dummy_img = Image.new('L', (512, 512), color='gray')
            dummy_img.save(input_image_path)
            input_tensor = preprocess_image(input_image_path)
            print(f"Dummy input image '{input_image_path}' preprocessed. Tensor shape: {input_tensor.shape}, dtype: {input_tensor.dtype}")
        except Exception as e_dummy:
            print(f"Could not create or process dummy input image: {e_dummy}")
            return
    except Exception as e:
        print(f"Error preprocessing input image: {e}")
        return

    # --- 4. 执行推理 ---
    try:
        print("Running inference...")
        results = session.run([output_name], {input_name: input_tensor})
        output_tensor = results[0] # session.run返回的是一个列表
        print(f"Inference complete. Output tensor shape: {output_tensor.shape}, dtype: {output_tensor.dtype}")
    except Exception as e:
        print(f"Error during ONNX inference: {e}")
        return

    # --- 5. 后处理输出 ---
    try:
        output_image = postprocess_output(output_tensor)
    except Exception as e:
        print(f"Error postprocessing output: {e}")
        return

    # --- 6. 保存输出图片 ---
    try:
        output_image.save(output_image_path)
        print(f"Output image saved to '{output_image_path}'")
    except Exception as e:
        print(f"Error saving output image: {e}")

if __name__ == "__main__":
    main()