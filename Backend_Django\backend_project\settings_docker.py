# Docker环境Django设置
# 专门为Docker容器环境优化的配置

from .settings import *

# 调试模式 - Docker环境可以开启以便排查问题
DEBUG = True

# 允许的主机 - Docker环境需要允许所有主机
ALLOWED_HOSTS = ['*']

# 数据库配置 - 使用挂载的Volume路径
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': '/app/db/db.sqlite3',  # 使用绝对路径指向挂载的Volume
        'OPTIONS': {
            'timeout': 30,  # 增加超时时间
        }
    }
}

# 文件存储路径 - 使用挂载的Volume路径
MEDIA_ROOT = '/app/models/custom_models'
SYSTEM_MODELS_ROOT = '/app/models/system_models'
EXAMPLE_IMAGES_ROOT = '/app/models/example_images'

# 静态文件设置
STATIC_ROOT = '/app/staticfiles'
STATIC_URL = '/static/'

# 日志配置 - 输出到控制台和文件
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/app/logs/django.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'vision_app': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}

# CORS设置 - Docker环境允许所有源
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8080",
    "http://127.0.0.1:8080",
    "http://*************:8080",
    "http://*************:8080",
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# 安全设置 - Docker开发环境可以放宽
SECURE_BROWSER_XSS_FILTER = False
SECURE_CONTENT_TYPE_NOSNIFF = False
X_FRAME_OPTIONS = 'SAMEORIGIN'

# 文件上传设置
FILE_UPLOAD_MAX_MEMORY_SIZE = 200 * 1024 * 1024  # 200MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 200 * 1024 * 1024  # 200MB

# 会话设置
SESSION_COOKIE_AGE = 86400 * 7  # 7天
SESSION_SAVE_EVERY_REQUEST = False

# 时区设置
USE_TZ = True
TIME_ZONE = 'Asia/Shanghai'

# 国际化设置
LANGUAGE_CODE = 'zh-hans'
USE_I18N = True
USE_L10N = True

# 缓存设置 - 使用内存缓存
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'docker-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000
        }
    }
}

# OCR任务配置 - 更新路径为Docker环境
OCR_TASK_CONFIGS = {
    'license_plate_cn': {
        'use_angle_cls': False,
        'det_algorithm': 'DB',
        'rec_algorithm': 'CRNN',
        'lang': 'ch',
        'use_gpu': False,
        'rec_image_shape': "3,64,320",
        'det_limit_type': "max",
        'det_limit_side_len': 320,
        'det_db_thresh': 0.01,
        'det_db_box_thresh': 0.01,
        'use_onnx': False,
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
        'rec_char_dict_path': '/app/models/system_models/ocr/car_liencese_ch/AI_OCR_Rec_CHNLP_NCHW_1x3x64x320/ppocr_keys_v1.txt',
    },

    'id_card_en': {
        'use_angle_cls': False,
        'det_algorithm': 'DB',
        'rec_algorithm': 'CRNN',
        'lang': 'ch',
        'use_gpu': False,
        'rec_image_shape': "3,48,320",
        'det_limit_side_len': 640,
        'det_db_thresh': 0.3,
        'det_db_box_thresh': 0.6,
        'det_db_unclip_ratio': 1.5,
        'use_space_char': True,
        'rec_char_dict_path': '/app/models/system_models/ocr/Identity_card_number_en/Identity_card_rec_model/en_dict.txt',
        'use_onnx': False,
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
    },
    'general_text_mobile_ch_en': { # PP-OCRv4 mobile模型配置（中英文）- Docker环境
        'use_angle_cls': False, # 轻量级模型通常不使用方向分类器
        'det_algorithm': 'DB',     # PP-OCRv4使用DB检测算法
        'rec_algorithm': 'SVTR_LCNet', # PP-OCRv4使用SVTR_LCNet识别算法
        'lang': 'ch', # 中文语言
        'use_gpu': False, # 默认CPU推理，适合移动端部署
        'rec_image_shape': "3,48,320", # PP-OCRv4 mobile标准输入尺寸
        'det_limit_side_len': 640,    # 检测模型输入限制
        'det_db_thresh': 0.3,        # DB检测阈值
        'det_db_box_thresh': 0.6,    # DB框阈值
        'det_db_unclip_ratio': 1.5,  # DB非裁剪比率
        'use_space_char': True,      # 使用空格符
        # PP-OCRv4 mobile使用标准中英文字典 - Docker路径
        'rec_char_dict_path': '/app/models/system_models/ocr/general_ocr_mobile_ch_en/PP-OCRv4_mobile_rec_inference_ch_en/ppocr_keys_v1.txt',
        'use_onnx': False, # 使用PaddlePaddle推理
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
        # PP-OCRv4 mobile特有参数
        'det_db_score_mode': 'fast', # 快速模式，适合移动端
        'rec_batch_num': 6, # 批处理数量
        'max_text_length': 25, # 最大文本长度
    },
    'general_text_mobile_en': { # PP-OCRv4 mobile模型配置（纯英文）- Docker环境
        'use_angle_cls': False, # 轻量级模型通常不使用方向分类器
        'det_algorithm': 'DB',     # PP-OCRv4使用DB检测算法
        'rec_algorithm': 'SVTR_LCNet', # PP-OCRv4使用SVTR_LCNet识别算法
        'lang': 'en', # 英文语言
        'use_gpu': False, # 默认CPU推理，适合移动端部署
        'rec_image_shape': "3,48,320", # PP-OCRv4 mobile标准输入尺寸
        'det_limit_side_len': 640,    # 检测模型输入限制
        'det_db_thresh': 0.3,        # DB检测阈值
        'det_db_box_thresh': 0.6,    # DB框阈值
        'det_db_unclip_ratio': 1.5,  # DB非裁剪比率
        'use_space_char': True,      # 使用空格符
        # PP-OCRv4 mobile使用英文字典 - Docker路径
        'rec_char_dict_path': '/app/models/system_models/ocr/general_ocr_mobile_en/PP-OCRv4_mobile_rec_inference_en/en_dict.txt',
        'use_onnx': False, # 使用PaddlePaddle推理
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
        # PP-OCRv4 mobile特有参数
        'det_db_score_mode': 'fast', # 快速模式，适合移动端
        'rec_batch_num': 6, # 批处理数量
        'max_text_length': 25, # 最大文本长度
    },
    'default': {
        'use_angle_cls': False,
        'det_algorithm': 'DB',
        'rec_algorithm': 'SVTR_LCNet',
        'lang': 'ch',
        'use_gpu': False,
        'rec_image_shape': "3,48,320",
        'det_limit_side_len': 960,
        'det_db_thresh': 0.3,
        'det_db_box_thresh': 0.6,
        'use_onnx': False,
        'use_npu': False,
        'use_mlu': False,
        'use_xpu': False,
        'return_word_box': False,
    }
}

print("=== Django Docker Settings Loaded ===")
print(f"DEBUG: {DEBUG}")
print(f"ALLOWED_HOSTS: {ALLOWED_HOSTS}")
print(f"DATABASE: {DATABASES['default']['NAME']}")
print(f"MEDIA_ROOT: {MEDIA_ROOT}")
print(f"SYSTEM_MODELS_ROOT: {SYSTEM_MODELS_ROOT}")
print(f"EXAMPLE_IMAGES_ROOT: {EXAMPLE_IMAGES_ROOT}")
