../../Scripts/paddleocr.exe,sha256=V2QALixjnonaGDUVoSwBxIOAayCQUsXeyZ3iDqbBjAY,108391
paddleocr-2.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
paddleocr-2.10.0.dist-info/LICENSE,sha256=_2MRxWwbIskgnJbMivwW2pHCTuVaFeFOvEb-GWDb2FY,11438
paddleocr-2.10.0.dist-info/METADATA,sha256=yeamHt2zR_O8sAnsODX-qKHfKBwALAcFKEv9BI1brRI,12252
paddleocr-2.10.0.dist-info/RECORD,,
paddleocr-2.10.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
paddleocr-2.10.0.dist-info/WHEEL,sha256=R0nc6qTxuoLk7ShA2_Y-UWkN8ZdfDBG2B6Eqpz2WXbs,91
paddleocr-2.10.0.dist-info/entry_points.txt,sha256=6gxQxYgf1Z0JLjwb2tg6WqaBXBjJuw3nrCPIMdSZgnQ,55
paddleocr-2.10.0.dist-info/top_level.txt,sha256=9iaw3B53tyEpdWJBsg8hBNdQv3SCgLx3CAHrOaIzbBM,10
paddleocr/LICENSE,sha256=_2MRxWwbIskgnJbMivwW2pHCTuVaFeFOvEb-GWDb2FY,11438
paddleocr/MANIFEST.in,sha256=O7V6GAj8LINNGqNtwje3WnMoMYgfol54i-mUrXU5RqY,399
paddleocr/README.md,sha256=v13U3Fk3U2RDl4GZmUO2l-vK5LDOazo8nV1Fbsg7VLc,10645
paddleocr/README_en.md,sha256=JfVdnVmest5XIJ6IEXJYMWjzxKvINGugjQ5DQqie61s,13848
paddleocr/__init__.py,sha256=GwMdwamY_69WLkzguUixlTNQm7oY9GCL1IrV_3ylo_0,1248
paddleocr/__pycache__/__init__.cpython-311.pyc,,
paddleocr/__pycache__/paddleocr.cpython-311.pyc,,
paddleocr/__pycache__/setup.cpython-311.pyc,,
paddleocr/paddleocr.egg-info/PKG-INFO,sha256=yeamHt2zR_O8sAnsODX-qKHfKBwALAcFKEv9BI1brRI,12252
paddleocr/paddleocr.egg-info/SOURCES.txt,sha256=A-w07tbrWCi6wJ9-7gSc265lQSbfLFbJMsSgBgLl2tY,8083
paddleocr/paddleocr.egg-info/dependency_links.txt,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
paddleocr/paddleocr.egg-info/entry_points.txt,sha256=6gxQxYgf1Z0JLjwb2tg6WqaBXBjJuw3nrCPIMdSZgnQ,55
paddleocr/paddleocr.egg-info/requires.txt,sha256=gSiylknHC_qzRKT5PdA8CWT95HfsYsC8QX9dKMxAdIQ,204
paddleocr/paddleocr.egg-info/top_level.txt,sha256=9iaw3B53tyEpdWJBsg8hBNdQv3SCgLx3CAHrOaIzbBM,10
paddleocr/paddleocr.py,sha256=m52MtftjqN2msi3vh_Ok2tUqLaR2FLrvk6LTYxw6iLI,42572
paddleocr/ppocr/__init__.py,sha256=Wg35JINTwCCrXtg-jyvUmem1FTYSYK5aXrhWRu-nATY,610
paddleocr/ppocr/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/data/__init__.py,sha256=h1f-PZa5WLzDCzCtFh9x78fK9HQ-Ydy-H0m8YxwFc30,5245
paddleocr/ppocr/data/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/data/__pycache__/collate_fn.cpython-311.pyc,,
paddleocr/ppocr/data/__pycache__/latexocr_dataset.cpython-311.pyc,,
paddleocr/ppocr/data/__pycache__/lmdb_dataset.cpython-311.pyc,,
paddleocr/ppocr/data/__pycache__/multi_scale_sampler.cpython-311.pyc,,
paddleocr/ppocr/data/__pycache__/pgnet_dataset.cpython-311.pyc,,
paddleocr/ppocr/data/__pycache__/pubtab_dataset.cpython-311.pyc,,
paddleocr/ppocr/data/__pycache__/simple_dataset.cpython-311.pyc,,
paddleocr/ppocr/data/collate_fn.py,sha256=AHbzZePJHgRo7mimuoBtn1GqR2I31rfNFX1ly3vG10o,5700
paddleocr/ppocr/data/imaug/ColorJitter.py,sha256=XtHtAXgRAUmX-Nl35jgcHhbBOLbixrY2PZs6Wd_Yv6Q,1028
paddleocr/ppocr/data/imaug/__init__.py,sha256=UbgD_5r7ejKLg3GUFr-fLzFEIetvkaHzg47AjG1p1S8,2752
paddleocr/ppocr/data/imaug/__pycache__/ColorJitter.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/abinet_aug.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/copy_paste.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/ct_process.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/drrg_targets.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/east_process.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/fce_aug.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/fce_targets.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/iaa_augment.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/label_ops.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/latex_ocr_aug.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/make_border_map.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/make_pse_gt.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/make_shrink_map.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/operators.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/pg_process.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/randaugment.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/random_crop_data.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/rec_img_aug.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/sast_process.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/ssl_img_aug.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/table_ops.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/__pycache__/unimernet_aug.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/abinet_aug.py,sha256=ia1K-GI89k1vhDpM_mr1MFkIcUA0qcijtstJAVq28WI,17942
paddleocr/ppocr/data/imaug/copy_paste.py,sha256=1cBEz_uSJyMwMTdaj6JcX6CV3B2bsr-21uvlbvpTQrU,6659
paddleocr/ppocr/data/imaug/ct_process.py,sha256=y6XdSCWPZKzMy8vsrdER1n7xC0NsFmenhuAer6wxTQg,11857
paddleocr/ppocr/data/imaug/drrg_targets.py,sha256=znH0wrOiTlfqCTCJZd5PmYMBRgP3aF_PCplY-curxyU,29635
paddleocr/ppocr/data/imaug/east_process.py,sha256=iZr56Fyj_Dq0L9KytO0mdXoJlzalIu7kwA-PFQjewas,17123
paddleocr/ppocr/data/imaug/fce_aug.py,sha256=P466WJUnOnJSsgO5Cnx1yTlU6je8ORv2AeN6cj6uXLE,20760
paddleocr/ppocr/data/imaug/fce_targets.py,sha256=G0sBSp51iQJdNpZUjWVia559er9wCq9APQjoDtxqkcY,26535
paddleocr/ppocr/data/imaug/frost_img/frost1.jpg,sha256=rryKrhC7kqxwuRFTNJGq2b2iOtvNy-jy3pWvYnsB4Dc,279928
paddleocr/ppocr/data/imaug/frost_img/frost2.png,sha256=_iEaibM2mZwgeoUs4FgY1FRdC1fFvq3YJLTMnZqbYTc,299002
paddleocr/ppocr/data/imaug/frost_img/frost3.png,sha256=LQ1QtKm7IT84sCTvd2hzG7g8wI0vJrV2a7wWfN-g5QQ,299002
paddleocr/ppocr/data/imaug/frost_img/frost4.jpg,sha256=P4uRyhqfpxZ7Cedz2lP1rmDQof2I8Cp4P24yinKIf24,36734
paddleocr/ppocr/data/imaug/frost_img/frost5.jpg,sha256=X8ahnfSkKbpoq9zI-KQnjUyfgcnMr9LJKrDIz4mS69I,155150
paddleocr/ppocr/data/imaug/frost_img/frost6.jpg,sha256=H5Ky9IQIdICFto3YHYFu8jn0LO8wKcJdBB_LZ2D7TyU,90185
paddleocr/ppocr/data/imaug/iaa_augment.py,sha256=o9X38gRQ2vn2tsPqAcqSFrytz8phBLAtjEuMzv-mZ1E,8881
paddleocr/ppocr/data/imaug/label_ops.py,sha256=pzTB1LPFIAztv9ZbUHuMmYVGh0KG633gT1xGIwSglKg,74284
paddleocr/ppocr/data/imaug/latex_ocr_aug.py,sha256=oe3ilNoKGKwc2yaq-LUr09-WZkn--H_UjGyypXR3twM,6310
paddleocr/ppocr/data/imaug/make_border_map.py,sha256=MBJHAyMP9FSgZ9ce3Oilfme020ItcfMlbMMNaNEVaCM,6457
paddleocr/ppocr/data/imaug/make_pse_gt.py,sha256=jrcAxUGY57_ZCDwIpZ_Hpgb8OAeMs2Dgz6xzSDU8CdM,3774
paddleocr/ppocr/data/imaug/make_shrink_map.py,sha256=62tbsJ86Rc9XMHlCfU2PDM_eVQcbuMZPyOIaNsIsdNU,4705
paddleocr/ppocr/data/imaug/operators.py,sha256=DSnNb3Wl8wAQ33yYys9sBjTM1yFU0F5xt-cJcYt6KvE,17194
paddleocr/ppocr/data/imaug/pg_process.py,sha256=a0jUirkg-xAn-tOTlybctEmRg0-ost02MJhw0hWFE_U,40535
paddleocr/ppocr/data/imaug/randaugment.py,sha256=Fce6zrvx7X-Ews2GRZgiha2IFWqoiPbdtoTyuND7_pg,5168
paddleocr/ppocr/data/imaug/random_crop_data.py,sha256=MX4aVT61Pl7_RtvX0Ea9yWD3RrJ5XZjJQewMkgaEx44,7806
paddleocr/ppocr/data/imaug/rec_img_aug.py,sha256=BKsQQnWacmbwu6Qhjm1mVDNFlpmUP9aN2HdBn60tO_U,28307
paddleocr/ppocr/data/imaug/sast_process.py,sha256=JveHth6gUkahhkAN2V4ktO3wXlqKJPxZ-CIdtv0Apho,29197
paddleocr/ppocr/data/imaug/ssl_img_aug.py,sha256=-KKTEHhfAYaymWSvv_jj79Qds8uJCnFqqAREQRkPwiw,1996
paddleocr/ppocr/data/imaug/table_ops.py,sha256=C-af4E5P9tQWAaL6XYH0xV5FxJGExJuIqRft1JaKpaw,8754
paddleocr/ppocr/data/imaug/text_image_aug/__init__.py,sha256=onTaFuMrSD6H8Tpdm_5DH4iUdP0YgYj-lUMMXFSQ3As,733
paddleocr/ppocr/data/imaug/text_image_aug/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/text_image_aug/__pycache__/augment.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/text_image_aug/__pycache__/warp_mls.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/text_image_aug/augment.py,sha256=ID0Xfzno678ogOol_M6D4pY0IGQZSNqC_d2NgUyUoog,3496
paddleocr/ppocr/data/imaug/text_image_aug/warp_mls.py,sha256=ZldVVHRhMkPVOWcxNvxPQBan5XGTdpRTonFuFWdWzvg,6810
paddleocr/ppocr/data/imaug/unimernet_aug.py,sha256=95fFX49DO6J-oR26pYHex_irkWzx3-Vtgct1fKMkxbc,26289
paddleocr/ppocr/data/imaug/vqa/__init__.py,sha256=Y3TgZZcHtk42cE7Vg2DxTj_meHpwReat9wgSyYPCgJQ,889
paddleocr/ppocr/data/imaug/vqa/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/vqa/__pycache__/augment.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/vqa/augment.py,sha256=nU2b7JuhP8WmPKaPeu57W4kg6whIFufKecvvMN9ONzk,1188
paddleocr/ppocr/data/imaug/vqa/token/__init__.py,sha256=vHq44SHu3aiTYX0EINrmlNGvz7UrKVo8RUto5a4j-zo,817
paddleocr/ppocr/data/imaug/vqa/token/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/vqa/token/__pycache__/vqa_re_convert.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/vqa/token/__pycache__/vqa_token_chunk.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/vqa/token/__pycache__/vqa_token_pad.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/vqa/token/__pycache__/vqa_token_relation.cpython-311.pyc,,
paddleocr/ppocr/data/imaug/vqa/token/vqa_re_convert.py,sha256=80SZnaCu6QNtggb7GDNpQ-PS_zB1zBIRQfOxTUh39sQ,1992
paddleocr/ppocr/data/imaug/vqa/token/vqa_token_chunk.py,sha256=3QYPCv503cfzeH62jzmUvgm9khbgMU-QAQcLlcgtD3g,5191
paddleocr/ppocr/data/imaug/vqa/token/vqa_token_pad.py,sha256=YOjIoAAN_RlopnmS6ovDVIJxEG_9YNYRCslBnvbDe-k,4854
paddleocr/ppocr/data/imaug/vqa/token/vqa_token_relation.py,sha256=5TkeH4xkjIYsYqBeJRuNIslX4MOsjNSCPdaOhsgaWEc,2650
paddleocr/ppocr/data/latexocr_dataset.py,sha256=MisIyWifv58re5QXR1azo96gR01X6qOp2jJ8c6FWMIE,6466
paddleocr/ppocr/data/lmdb_dataset.py,sha256=q2wa9hxnjRRVqE-Sj8qmLibxbymteB1qv0hUisiGaBY,10516
paddleocr/ppocr/data/multi_scale_sampler.py,sha256=pVOUw8FstO0qTD40Luw0ukYzPOgKIJ2QwV21VI19JVI,6580
paddleocr/ppocr/data/pgnet_dataset.py,sha256=Gx36CnuBapA4wrYaLo1qJF7-iMn4sZSzKRd2XlUqIRs,4185
paddleocr/ppocr/data/pubtab_dataset.py,sha256=sA5612PN25Mob46axJxyB-L8WRFmRcVy1LQJkNBRTls,5145
paddleocr/ppocr/data/simple_dataset.py,sha256=bULCv3mdRhAJnSkCyGXAmlzdOhzFIrUcVQOHyEKHuzk,9972
paddleocr/ppocr/postprocess/__init__.py,sha256=y6oV3ImnQjIk0sjXdEQOJPaytel9G9ZeCqJLVWk1sO4,3679
paddleocr/ppocr/postprocess/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/cls_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/ct_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/db_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/drrg_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/east_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/fce_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/locality_aware_nms.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/pg_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/picodet_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/rec_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/sast_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/table_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/vqa_token_re_layoutlm_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/__pycache__/vqa_token_ser_layoutlm_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/cls_postprocess.py,sha256=oD05heAEQ7jXl5G9aPioFt9e1aSk_jFMOcF9CSCK1oA,1526
paddleocr/ppocr/postprocess/ct_postprocess.py,sha256=lykTcMJdTgcPzg9RVU-EPhYZvhP83LCLrf_pBhFYP9E,5377
paddleocr/ppocr/postprocess/db_postprocess.py,sha256=pxI8-rR8nzJvlEL04-yWNpTFOhJVb8E-tUEWmo5mtn8,9852
paddleocr/ppocr/postprocess/drrg_postprocess.py,sha256=-Ylk83UCF_Sfm4zlcFuAd3LIU8rvfwndEeiJ-KQK4z4,11788
paddleocr/ppocr/postprocess/east_postprocess.py,sha256=YMtY3iSnNZ349FeQJrghMX50RtnV8_MBBXEL1aO0LNY,5110
paddleocr/ppocr/postprocess/fce_postprocess.py,sha256=_QkX7GdoyNIzKzkfG2TtowzWqYnwRfxUex4GSedxGKQ,8481
paddleocr/ppocr/postprocess/locality_aware_nms.py,sha256=DpyPLBRiFLSb9uLkQi7vfcWFHKjIR3S0SasFIM91J2c,5026
paddleocr/ppocr/postprocess/pg_postprocess.py,sha256=8jFQxqqVIUWxOq3RhClIAPuM0ouY2_f7gsSXgNKMZNk,2026
paddleocr/ppocr/postprocess/picodet_postprocess.py,sha256=PGGhuDiBOFzBmMdxApqsgJvOr7pLCd6Opy94ENWGSFs,11618
paddleocr/ppocr/postprocess/pse_postprocess/__init__.py,sha256=zduL2gW3id-12X8df-gct8r5NlPe0uAEHa5s7qayRRs,655
paddleocr/ppocr/postprocess/pse_postprocess/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/postprocess/pse_postprocess/__pycache__/pse_postprocess.cpython-311.pyc,,
paddleocr/ppocr/postprocess/pse_postprocess/pse/README.md,sha256=w7U_giAwPC_B6OT44fkHjRClo1gqRHrK5NuEINuYZ7o,160
paddleocr/ppocr/postprocess/pse_postprocess/pse/__init__.py,sha256=u7QfMlDi94C4di9IQ7uiPVnWejRH8JM3T35vATWEU1k,1166
paddleocr/ppocr/postprocess/pse_postprocess/pse/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/postprocess/pse_postprocess/pse/__pycache__/setup.cpython-311.pyc,,
paddleocr/ppocr/postprocess/pse_postprocess/pse/pse.pyx,sha256=mLPkLzJH-Hch7EVbLOF0mqUSTP0uri3pj8zvWDKqxeg,2577
paddleocr/ppocr/postprocess/pse_postprocess/pse/setup.py,sha256=jxiIgxPB81-aCzbuQJ_7Hj0aTMSQV9Nx2iwtEXaQG4Y,419
paddleocr/ppocr/postprocess/pse_postprocess/pse_postprocess.py,sha256=TKDQrMb4GO4SfW7XFf5gcwTayY0EePSPkh7ijmd-vXc,3975
paddleocr/ppocr/postprocess/rec_postprocess.py,sha256=nvOAjShp3BftHB_lLUSmcoa2RzQWwl9Vvlv710fumKE,55222
paddleocr/ppocr/postprocess/sast_postprocess.py,sha256=ieAoBMC4Gj4vmwpqI95Q6_nQdsjcvCRFB099Tf9Hm7w,13408
paddleocr/ppocr/postprocess/table_postprocess.py,sha256=EwNQpZ41VYlO0vHY94RmgGpOjvFg1nhSVzjfqiC9SfM,6844
paddleocr/ppocr/postprocess/vqa_token_re_layoutlm_postprocess.py,sha256=yaNXWCH5qHn84fqfuq5mHqaTpfF5G6nsOfdIVTgFX_E,3772
paddleocr/ppocr/postprocess/vqa_token_ser_layoutlm_postprocess.py,sha256=0aQEsvTE7akHEu_2Qtps5ps4dvfGn6BhHjyiKO6G-us,4382
paddleocr/ppocr/utils/EN_symbol_dict.txt,sha256=hR38HrsQUkVBDizeIguGEBlLilD51wueg5eVu8s1h_s,188
paddleocr/ppocr/utils/__init__.py,sha256=0VZX0LjwnMDtsjtwAbY-srruUpfTl4DxsGgAZfhi2zE,610
paddleocr/ppocr/utils/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/export_model.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/gen_label.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/iou.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/logging.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/network.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/poly_nms.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/profiler.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/save_load.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/stats.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/utility.cpython-311.pyc,,
paddleocr/ppocr/utils/__pycache__/visual.cpython-311.pyc,,
paddleocr/ppocr/utils/dict/README.md,sha256=kcmk5ayn5kpi7w_3q69cMv8tCmP-z7gxWAgVevMNbTs,305
paddleocr/ppocr/utils/dict/ar_dict.txt,sha256=bo0k6OtFY1zugffKxWlPYy-sf9DGZKsrK3mKvSj5f_A,273
paddleocr/ppocr/utils/dict/arabic_dict.txt,sha256=Y3wnyIUSwiCJvvkns0raCPdI3BMqxw-s1o2CAjhMJyY,405
paddleocr/ppocr/utils/dict/be_dict.txt,sha256=L-EPTJ8_j4gwZF-t-sSoxMo93q2acrTt2NIkS1BuWts,357
paddleocr/ppocr/utils/dict/bengali_dict.txt,sha256=K1MaKGCa6OTqfadoaueTLOf5PYoSHY8-0mpkswdXlrs,296
paddleocr/ppocr/utils/dict/bg_dict.txt,sha256=xGrw5wyyiEBFkcFZT6_LtYLzRPIW3Xf6zQSGgN_NDHI,341
paddleocr/ppocr/utils/dict/bm_dict.txt,sha256=P3mj-Kpx7jPoQY-cEuR0EtFV4W37RnwUuAuh43Xm7OM,640
paddleocr/ppocr/utils/dict/bm_dict_add.txt,sha256=cF2V__7iFhwHtXhccDanwiAfCqgsiJVkjX3UvT3jfnw,12789
paddleocr/ppocr/utils/dict/bn_dict.txt,sha256=1QmrrWAc8zFYET15bOdlhcT1ClGzVoACtNT8_7xUidk,4051
paddleocr/ppocr/utils/dict/chinese_cht_dict.txt,sha256=gyVR_uHy-8l1CHctgevcjboSwA3pejXHHJ3fQ92sGoM,33443
paddleocr/ppocr/utils/dict/confuse.pkl,sha256=V9iqmKeBMw91NKgSYxeed8FZo3BQvTH-Cdjd8YgMVig,30912
paddleocr/ppocr/utils/dict/cyrillic_dict.txt,sha256=NpqCxsjEeXhKXXJkSLg7Hq-1_vCkEppeqjkpYl3c0TI,410
paddleocr/ppocr/utils/dict/devanagari_dict.txt,sha256=tfG-bYu_8aGfuWxdTKlqQjOAI0u30s4OB7WDittNGOo,508
paddleocr/ppocr/utils/dict/en_dict.txt,sha256=4fmwnXV-YLibbFxwG0Dk2mLkT5_1Vrc5WcUO3aEs1TA,126
paddleocr/ppocr/utils/dict/fa_dict.txt,sha256=5GN52nOPr1VlYEaOMz4K3XX-smmDSSmDdKOLGdlF3K4,330
paddleocr/ppocr/utils/dict/french_dict.txt,sha256=t3hwpp-DMSDrISCIms0Zrnn2zZ_e3AO9xrO0CusMkt8,324
paddleocr/ppocr/utils/dict/german_dict.txt,sha256=efmYi_hYebDe1iBMCq38qR64bx5MmCaL5_4ZTMxnCWI,346
paddleocr/ppocr/utils/dict/gujarati_dict.txt,sha256=65gB10SZHd1L-Zr3V3RT0DXr-f7FoXkCqU4BVSt-K1Y,198
paddleocr/ppocr/utils/dict/hebrew_dict.txt,sha256=2DjWAYhRoJKfcIkBASggDO0Tl3eMd0HI6Sfx8W4cfSI,610
paddleocr/ppocr/utils/dict/hi_dict.txt,sha256=ICAmNgEM_xIpNhU6-nJk-b_JmOzxcrPRw0M8KNo0sRw,488
paddleocr/ppocr/utils/dict/it_dict.txt,sha256=T-37GSDt3W3O04xzyqw5q0Iv3j3im2kBcnymTekTko0,266
paddleocr/ppocr/utils/dict/japan_dict.txt,sha256=Hc_LQe7JBXapRbMITyKt4RztUG4k8Uh5JFsHFpjzCOg,17332
paddleocr/ppocr/utils/dict/ka_dict.txt,sha256=-tP3ZZQyde7CE0pvvJtSeFQmZBEE1OfbOhWdxz80Hd4,452
paddleocr/ppocr/utils/dict/kazakh_dict.txt,sha256=9tvWKGjQVlEt2un4NO-jL2JKMlRpTuqsoYMUdGrTGlU,126
paddleocr/ppocr/utils/dict/kie_dict/xfund_class_list.txt,sha256=39bed1NkOeOZih9Ot8a-vU2AQVVOoa7u7eK8Sbf3-Q0,29
paddleocr/ppocr/utils/dict/korean_dict.txt,sha256=qh_ciuj3zUCg7E7bRy6wQh4RQn5sz-6ZFUQHQsGLCiA,14480
paddleocr/ppocr/utils/dict/latex_ocr_tokenizer.json,sha256=UK_0t_dTzCc4jN5cT9p8yOGcWQTKHZk8szIYem0-sGA,24175
paddleocr/ppocr/utils/dict/latex_symbol_dict.txt,sha256=IKsuICczZRA8NGyt2J4KFkkGztSrXh5s7Bjt9ILykaA,366
paddleocr/ppocr/utils/dict/latin_dict.txt,sha256=jm1ONil4jDXDH35TAofWFHtUm7eiZb1nCLsoETRCniw,468
paddleocr/ppocr/utils/dict/layout_dict/layout_cdla_dict.txt,sha256=TAhD1lsYxeGDPmbG7ZLJ8pS7XLq5GL0WoQACxbdOKRU,86
paddleocr/ppocr/utils/dict/layout_dict/layout_publaynet_dict.txt,sha256=yaNViV3ikp4utDGO-hvd0WFvtMGrKIBeYbyK_ISViTU,29
paddleocr/ppocr/utils/dict/layout_dict/layout_table_dict.txt,sha256=ftmsODH32FJWlrOQzeLs5MHfEypDMmgHKke01CpuiTE,6
paddleocr/ppocr/utils/dict/mr_dict.txt,sha256=6q7iazYbLBgt1w-SnZDHFnJnFkPeALwnX4WQwJWfGHI,452
paddleocr/ppocr/utils/dict/ne_dict.txt,sha256=B5x3lET8mzn2Uy86h6peVXYwZi60q9YLHE6-5xz7usY,452
paddleocr/ppocr/utils/dict/oc_dict.txt,sha256=4edlpKr9AEBiStRfie5NFvVPIvFy_37Pgts39zs3XeU,210
paddleocr/ppocr/utils/dict/parseq_dict.txt,sha256=hR38HrsQUkVBDizeIguGEBlLilD51wueg5eVu8s1h_s,188
paddleocr/ppocr/utils/dict/ppocrv4_doc_dict.txt,sha256=pbw4h8Q8kB5aP5exP_rfHFdU7efMjJ9aviLodafEg3I,62346
paddleocr/ppocr/utils/dict/pu_dict.txt,sha256=RzLqLEl-0x-WFQvb9D4ZHvf51l_t_LIdCS6Hhe_oeT8,305
paddleocr/ppocr/utils/dict/rs_dict.txt,sha256=lRi9LEN9NFXRWadwQBfBauSnTkssSU09tUvgGB3Nbxs,194
paddleocr/ppocr/utils/dict/rsc_dict.txt,sha256=yWYzVbs3BpYOV69eRwT-sCyf8-rQMprdvhzSvuaIQ8U,324
paddleocr/ppocr/utils/dict/ru_dict.txt,sha256=0G80uwoT4UYamcFJd0R3NrU16APVGt6dT3Rb6qPafkQ,313
paddleocr/ppocr/utils/dict/samaritan_dict.txt,sha256=HZD7YB2RxOqR15B5akIUam-Xeh7jjhTNLp83qkUVaDA,649
paddleocr/ppocr/utils/dict/spin_dict.txt,sha256=3OuSVcYxkkXdo7VZtkrqlNQIlV1vMxMA1ZTIENH1sEs,136
paddleocr/ppocr/utils/dict/syriac_dict.txt,sha256=cBQQztqUF5CyG2TtvjAHLYla1lkT2HgYwu52GZVJEYc,393
paddleocr/ppocr/utils/dict/ta_dict.txt,sha256=6T5pSBSv2f8ekYtvS7Qmf7TGWpNE7l10ZPkTW5DAonA,352
paddleocr/ppocr/utils/dict/table_dict.txt,sha256=azeGUpDcailsfxKLLGwvdxjZ1nCAiOGPvzBW_DGMKOc,894
paddleocr/ppocr/utils/dict/table_master_structure_dict.txt,sha256=I04lqkYIB0Cpkmk81MGnu8kcQ1rEQVjt0fQvAWPV6K4,435
paddleocr/ppocr/utils/dict/table_structure_dict.txt,sha256=HiN14L4OpmJ_2lxapgq95lxi3vrvwBXQDLSbiHNrwXA,298
paddleocr/ppocr/utils/dict/table_structure_dict_ch.txt,sha256=aNNEqEtybgQ_OQEiJA_yss7SlJsqgM6bYa6VUFTRkO8,578
paddleocr/ppocr/utils/dict/te_dict.txt,sha256=7plGpg13AZd0dOiYg2lKTKIOqjhoojM0v3lA3NAI8Pk,429
paddleocr/ppocr/utils/dict/th_dict.txt,sha256=uHCQvp61Tfvot3myjd3l4fc4cmDxwrN9SZECUBMeFwg,330
paddleocr/ppocr/utils/dict/ug_dict.txt,sha256=QL8U4nGR7Tyg22LDcBX0tBr7ExC9yPwZUYHa1aHYLcM,301
paddleocr/ppocr/utils/dict/uk_dict.txt,sha256=5IXkdeT5ZWhUM4cUkJ6oxTbOvLgzxHqhUSB8MuAkf1s,348
paddleocr/ppocr/utils/dict/unimernet_tokenizer/tokenizer.json,sha256=KBHYJwHsl8GS-iVqorRRaSk3OHCuZgMmzFsdyHm5X_I,2140014
paddleocr/ppocr/utils/dict/unimernet_tokenizer/tokenizer_config.json,sha256=_U2U-Lnbt97rOj7whMoOFsQ9RXdKGAcwt-nP1jWaB0s,4491
paddleocr/ppocr/utils/dict/ur_dict.txt,sha256=Y9L8quqlteyAVjD4c24IS_lhcTlmTN7kWtjrxvRcrJ8,332
paddleocr/ppocr/utils/dict/vi_dict.txt,sha256=wg5kcz2k3EcGfH-O55wcVJgDzNV6kQv32ZijrfS9oBs,339
paddleocr/ppocr/utils/dict/xi_dict.txt,sha256=1mXZ301J-W_zzR3PLVMITQD-FnB0fdmpO1i53aEO-hU,250
paddleocr/ppocr/utils/dict90.txt,sha256=dMsVG7Lu3P8hWeLTYADPnD4_Wc4xnHYYMELDse9n-vE,180
paddleocr/ppocr/utils/e2e_metric/Deteval.py,sha256=bZ3UrwuYvoaqp09jV0WSOivCP2EjpAZ8LEAamrp9EJc,31928
paddleocr/ppocr/utils/e2e_metric/__pycache__/Deteval.cpython-311.pyc,,
paddleocr/ppocr/utils/e2e_metric/__pycache__/polygon_fast.cpython-311.pyc,,
paddleocr/ppocr/utils/e2e_metric/polygon_fast.py,sha256=e-wbNE4KHfFOsYLQHUfPhNOHjYOFJlwROQzlnE4N0M0,2838
paddleocr/ppocr/utils/e2e_utils/__pycache__/extract_batchsize.cpython-311.pyc,,
paddleocr/ppocr/utils/e2e_utils/__pycache__/extract_textpoint_fast.cpython-311.pyc,,
paddleocr/ppocr/utils/e2e_utils/__pycache__/extract_textpoint_slow.cpython-311.pyc,,
paddleocr/ppocr/utils/e2e_utils/__pycache__/pgnet_pp_utils.cpython-311.pyc,,
paddleocr/ppocr/utils/e2e_utils/__pycache__/visual.cpython-311.pyc,,
paddleocr/ppocr/utils/e2e_utils/extract_batchsize.py,sha256=cbSERQ5XvVFRs4sRhflMZ9cu_uZpEddGLA6yL8y476g,3236
paddleocr/ppocr/utils/e2e_utils/extract_textpoint_fast.py,sha256=zBlCHmh_U9BMRRg8nlqy-GwOjBOY0J_XY_0eERgwMDE,18639
paddleocr/ppocr/utils/e2e_utils/extract_textpoint_slow.py,sha256=83aRlVNzqeyEGI6rHFKJmeOzWQO7sDctdEiu5dgUet8,21515
paddleocr/ppocr/utils/e2e_utils/pgnet_pp_utils.py,sha256=Tk15ElYZAZr1OsNYcMuoFeyTCIAVeTcMA5SmAelyfzg,6496
paddleocr/ppocr/utils/e2e_utils/visual.py,sha256=7nbPtHO_4x9kB6druPlvfzTIPrM9c4PaI2t-JPOtEJ0,5266
paddleocr/ppocr/utils/en_dict.txt,sha256=VmLfnS0D8OjKDTsGSdasurkEtqFLPTUhRjxxw3xmjOM,190
paddleocr/ppocr/utils/export_model.py,sha256=Hk-gsGCpTWomy_zpqYz0D-SBTWfckRneinHH-u4L8Fc,21598
paddleocr/ppocr/utils/formula_utils/__pycache__/math_txt2pkl.cpython-311.pyc,,
paddleocr/ppocr/utils/formula_utils/__pycache__/unimernet_data_convert.cpython-311.pyc,,
paddleocr/ppocr/utils/formula_utils/math_txt2pkl.py,sha256=i08hQm2mYDrZ_KRVALbij1lcmLt10XbyAL3q1l77mc0,2588
paddleocr/ppocr/utils/formula_utils/unimernet_data_convert.py,sha256=c0xHtJzxOoRaz0FA36JhZP9eHytZSAQbrG532X9lp2M,3778
paddleocr/ppocr/utils/gen_label.py,sha256=mPPPQiXLEczcHgKupFsBLFfpB0ZglfJ95ooDRs2ZsN4,2935
paddleocr/ppocr/utils/ic15_dict.txt,sha256=cemqgz7EJIRPNnC7XxooUYZ7nOlj9431MeRdOkx33XI,72
paddleocr/ppocr/utils/iou.py,sha256=op6MqJuPJ6b6nwYM81Sds7sVvTocLRrVqLdH5aE10sA,1699
paddleocr/ppocr/utils/loggers/__init__.py,sha256=luA0_aSYMaZvlVceR3v6KTpejjSfzwhtMLOjZhKQnQE,67
paddleocr/ppocr/utils/loggers/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppocr/utils/loggers/__pycache__/base_logger.cpython-311.pyc,,
paddleocr/ppocr/utils/loggers/__pycache__/loggers.cpython-311.pyc,,
paddleocr/ppocr/utils/loggers/__pycache__/wandb_logger.cpython-311.pyc,,
paddleocr/ppocr/utils/loggers/base_logger.py,sha256=MDwfFcRts2wch6fdvUp1E2fFLAyHZgmQDp005PHyiWc,326
paddleocr/ppocr/utils/loggers/loggers.py,sha256=UOU2NsIncccFU9oPmS6rBGnK5q_1Go3NRQGQI6zXs0A,576
paddleocr/ppocr/utils/loggers/wandb_logger.py,sha256=QVdvAE6QMUk7tj5Qw-ZyOR52ADYibTcUd0K-tiwVc-M,2451
paddleocr/ppocr/utils/logging.py,sha256=gnX7nbRhQL9sGqo81qChJGrbuWA7eAOIniSKF352hiA,2979
paddleocr/ppocr/utils/network.py,sha256=cFzy1YKYU1M7Hzib8VafFMZJLOYR-DFGyZG0Y4gAUkE,5401
paddleocr/ppocr/utils/poly_nms.py,sha256=EqkCglK83ASvr3A3Ev1Lv8Pm04cXS0fY2_BpUpVvic0,4136
paddleocr/ppocr/utils/ppocr_keys_v1.txt,sha256=ochNm9uaspBDxYiWIk0ylBeD64IWKWGEFtywjxKIZJI,26250
paddleocr/ppocr/utils/profiler.py,sha256=t_nYMONNwt4Xp9GuZhS9qQ2ZNnVMvgeuDsLUVDrtk1s,5246
paddleocr/ppocr/utils/save_load.py,sha256=Jm9TqpAGG_YvZ1Kl14Tdr8iMJD-NrxxwXXp0IqeJfig,14217
paddleocr/ppocr/utils/stats.py,sha256=1yJdZbpzZhHIwi3doICkv4lHOd6f1EbRdL92NauH_PE,2199
paddleocr/ppocr/utils/utility.py,sha256=dPNfJnYd-FIEWim95QUqn8Y6EsslSlHWnEIkL5CvZ-E,7440
paddleocr/ppocr/utils/visual.py,sha256=aOLlwFvZlNqTZkUHo6iNPD4MrNOvGxJn-E4rxf-OORQ,4605
paddleocr/ppstructure/README.md,sha256=GDt2Dnx0M-EjgmxJR6-YbaKP1NSJ2S_MmI5CdCUKzAY,203
paddleocr/ppstructure/__init__.py,sha256=sy5qnWoxEL6vi_eHtLNroLJC03VNa7_aYJ6NVgztj5k,608
paddleocr/ppstructure/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppstructure/__pycache__/predict_system.cpython-311.pyc,,
paddleocr/ppstructure/__pycache__/utility.cpython-311.pyc,,
paddleocr/ppstructure/kie/README.md,sha256=jEfF0YqzUEC89Yl1JFXNb159nfE4ehfXWcZH3wT90X8,12634
paddleocr/ppstructure/kie/README_ch.md,sha256=dN_DIzYqCVREqrp7G2ZS1jWvPGekKaBmueYvts-WdZM,11520
paddleocr/ppstructure/kie/__pycache__/predict_kie_token_ser.cpython-311.pyc,,
paddleocr/ppstructure/kie/__pycache__/predict_kie_token_ser_re.cpython-311.pyc,,
paddleocr/ppstructure/kie/how_to_do_kie.md,sha256=k7vzfQq5PormaPq1rUtvrrzACceNa70EEuL3_d_Yq2Y,13194
paddleocr/ppstructure/kie/how_to_do_kie_en.md,sha256=ays6EzGC6OlBlM72-IY4H_LmhF0aLcSsuJozc-CAhBc,13529
paddleocr/ppstructure/kie/predict_kie_token_ser.py,sha256=aFmZ8zibKyeuIebz7m_g1UCQqajrrVZdsYSvSdEJNOA,6330
paddleocr/ppstructure/kie/predict_kie_token_ser_re.py,sha256=sTC2blysBknacfw7GBzfklkTJyZXW5tATZdJc6rUNbk,5006
paddleocr/ppstructure/kie/requirements.txt,sha256=Lxh3yiks_WonHmPCUYpkSupeL-7rSWWK5VH51WaPTH8,75
paddleocr/ppstructure/kie/tools/__pycache__/eval_with_label_end2end.cpython-311.pyc,,
paddleocr/ppstructure/kie/tools/__pycache__/trans_funsd_label.cpython-311.pyc,,
paddleocr/ppstructure/kie/tools/__pycache__/trans_xfun_data.cpython-311.pyc,,
paddleocr/ppstructure/kie/tools/eval_with_label_end2end.py,sha256=-zrtvW4kLC5cZvPSLCOITzlmvc5MGsL5I_1rLYWPDZ4,8190
paddleocr/ppstructure/kie/tools/trans_funsd_label.py,sha256=LCRvdiPP-dkGG57uHdgGWLGMGZVwHksQcSNnZoq3x7k,6374
paddleocr/ppstructure/kie/tools/trans_xfun_data.py,sha256=U0P0CMUxAOVIFXKlynozSn_dxGC0rNTxKH_B8-4wm08,2174
paddleocr/ppstructure/layout/README.md,sha256=fXYTD0oo-10Qv5r7lNofgB3yyk3xtc-EVvi1VhfkHkQ,23309
paddleocr/ppstructure/layout/README_ch.md,sha256=A320A0HrqmkhtBqP9QWhdyJHMUFq0epHenVcOBX890k,21011
paddleocr/ppstructure/layout/__init__.py,sha256=sy5qnWoxEL6vi_eHtLNroLJC03VNa7_aYJ6NVgztj5k,608
paddleocr/ppstructure/layout/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppstructure/layout/__pycache__/predict_layout.cpython-311.pyc,,
paddleocr/ppstructure/layout/predict_layout.py,sha256=VVFM2NIeLcCvEQwtAnOkickcBxNXQp4zWVWsrCTYmlc,4729
paddleocr/ppstructure/pdf2word/README.md,sha256=Emcl4ICOjJn4l7ONhA6n9wHns7kGjgB2zfuXEqyq_7k,2714
paddleocr/ppstructure/pdf2word/__pycache__/pdf2word.cpython-311.pyc,,
paddleocr/ppstructure/pdf2word/icons/chinese.png,sha256=TugHqQ-WwpE2uP6vggfDOAOaLe1Sb27PCACdYeGDiKE,2670
paddleocr/ppstructure/pdf2word/icons/english.png,sha256=4ys3NdI8eXccsEmuyGwYIuKx5dT0OVlq675VnxYMMIg,2888
paddleocr/ppstructure/pdf2word/icons/folder-open.png,sha256=FWAd1hIpat8E9gLikZYtk87fhi9bctKgyw5-M-ULArk,2491
paddleocr/ppstructure/pdf2word/icons/folder-plus.png,sha256=6JvyIwoWQ3B_oGKCXpDqdGuKRMh93teIKKde-ZnKD_0,2598
paddleocr/ppstructure/pdf2word/pdf2word.py,sha256=Orlr62SmtVPLrOd0JE9RW55eFhQf4wCQui-Ft0dWX2g,20526
paddleocr/ppstructure/predict_system.py,sha256=6LziM57CBY8ZNykk2_8DcHpP_jfXcy_K3-NJuZQxhsg,15748
paddleocr/ppstructure/recovery/README.md,sha256=-249cfRMJt2UyIRhsqyN3zBSMjdfN3imRcQ4B7TfjUA,10239
paddleocr/ppstructure/recovery/README_ch.md,sha256=HsPEMbOejc3eyOFIEU5inoIFXrxIjTjIOJu_DkGMOqE,9888
paddleocr/ppstructure/recovery/__init__.py,sha256=sy5qnWoxEL6vi_eHtLNroLJC03VNa7_aYJ6NVgztj5k,608
paddleocr/ppstructure/recovery/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppstructure/recovery/__pycache__/recovery_to_doc.cpython-311.pyc,,
paddleocr/ppstructure/recovery/__pycache__/recovery_to_markdown.cpython-311.pyc,,
paddleocr/ppstructure/recovery/__pycache__/table_process.cpython-311.pyc,,
paddleocr/ppstructure/recovery/recovery_to_doc.py,sha256=9x2C4xFVGT8qyqBxOagQdx5UC0R3BzIZ5UpLiXFDowU,5548
paddleocr/ppstructure/recovery/recovery_to_markdown.py,sha256=q1HxGwvfqVwrzLqWJ_igCsEj5BPN3x_-f_lVlzKipJw,5993
paddleocr/ppstructure/recovery/requirements.txt,sha256=fnLcPR3h1qD-OM0a_6K56l6IfIwyhxXr8VNvEsNibzs,57
paddleocr/ppstructure/recovery/table_process.py,sha256=hkrxGEG6nJ434Z3YebnCc1S57gNE7cfhezEmp1dzcgg,11541
paddleocr/ppstructure/table/README.md,sha256=snCQkuKTADnRJHT1pTP4cldSlOaQZ0Jb77IKovcQf30,8183
paddleocr/ppstructure/table/README_ch.md,sha256=85n4X_4XmAOc-RL7px7PM2wsvRXX_Cx_dkrkHj2me8s,7476
paddleocr/ppstructure/table/__init__.py,sha256=sy5qnWoxEL6vi_eHtLNroLJC03VNa7_aYJ6NVgztj5k,608
paddleocr/ppstructure/table/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppstructure/table/__pycache__/convert_label2html.cpython-311.pyc,,
paddleocr/ppstructure/table/__pycache__/eval_table.cpython-311.pyc,,
paddleocr/ppstructure/table/__pycache__/matcher.cpython-311.pyc,,
paddleocr/ppstructure/table/__pycache__/predict_structure.cpython-311.pyc,,
paddleocr/ppstructure/table/__pycache__/predict_table.cpython-311.pyc,,
paddleocr/ppstructure/table/__pycache__/table_master_match.cpython-311.pyc,,
paddleocr/ppstructure/table/convert_label2html.py,sha256=zuo-teP5TZ9Ibhd1_cGcRXjHLaek0yhJtC7cF8FBC_c,3040
paddleocr/ppstructure/table/eval_table.py,sha256=2sQJmj-xd7dRMUfY87KMW1Q11NIVjOVy3-GZy2IBTmI,3446
paddleocr/ppstructure/table/matcher.py,sha256=NBKDBwvZZap1lo0pVVI-NAzG1URtVW8FX3ntIvrKYmo,8186
paddleocr/ppstructure/table/predict_structure.py,sha256=yP9SfIcpUdqPCyt9wzmihYYM3mqyuUXV8TBcHhLvfJc,7449
paddleocr/ppstructure/table/predict_table.py,sha256=ZdBqev_5k2kS-C-fwVsU_-SRXYnGGH8FuVHdggMGKuI,8586
paddleocr/ppstructure/table/table_master_match.py,sha256=DEokfV1Ax2MBi94u7pAjunsbixlzNJ2cOgyECtwUa_M,36499
paddleocr/ppstructure/table/table_metric/__init__.py,sha256=T4hcU6Vv_EjRXlt-TvWvYqhiffAfueBtshJ3rFOP-BI,659
paddleocr/ppstructure/table/table_metric/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppstructure/table/table_metric/__pycache__/parallel.cpython-311.pyc,,
paddleocr/ppstructure/table/table_metric/__pycache__/table_metric.cpython-311.pyc,,
paddleocr/ppstructure/table/table_metric/parallel.py,sha256=ZsXppzcrJ7wwK69RNoFk4L-cQR55y99949gj_cj2re8,2143
paddleocr/ppstructure/table/table_metric/table_metric.py,sha256=QFy-p2cVuy6JVvfi7RvDatqC3QiHnuY_jrMlOPi7IdY,8803
paddleocr/ppstructure/table/tablepyxl/__init__.py,sha256=sy5qnWoxEL6vi_eHtLNroLJC03VNa7_aYJ6NVgztj5k,608
paddleocr/ppstructure/table/tablepyxl/__pycache__/__init__.cpython-311.pyc,,
paddleocr/ppstructure/table/tablepyxl/__pycache__/style.cpython-311.pyc,,
paddleocr/ppstructure/table/tablepyxl/__pycache__/tablepyxl.cpython-311.pyc,,
paddleocr/ppstructure/table/tablepyxl/style.py,sha256=A6ip70MK6aNeeIGIAQwCM0S7ps0MXRc27Ue9F5BGt5A,10673
paddleocr/ppstructure/table/tablepyxl/tablepyxl.py,sha256=s9OIStWeqA-Zil6e4HIbVOuE0Lh-W9CIi9iJfg2XKik,4402
paddleocr/ppstructure/utility.py,sha256=Hsurxs8OLAsN_rsbp6TSUefbJs70E940IYuNw5eYUjE,10248
paddleocr/pyproject.toml,sha256=-1xAk0_5XM0G4yElhGbIhBHZarNeEiw2arcQ3SZ83hw,2027
paddleocr/requirements.txt,sha256=j-RvVkT4LJjP-tW_wDOsINo8USk678UcOdLwB0Wqw0I,196
paddleocr/setup.cfg,sha256=HEc8uu6NpfxG5_AVh5SvXOpEFMNKPPPxgMIAH144vT4,38
paddleocr/setup.py,sha256=uF3AS7k-UeS7Zjmp4rK8tCRWcLK-Z0KX2B_DVVHFAU0,650
paddleocr/tools/__init__.py,sha256=evzhuhOIEfNZEW2VC69xipzKczzobr4IcYgUinKrjGM,694
paddleocr/tools/__pycache__/__init__.cpython-311.pyc,,
paddleocr/tools/__pycache__/eval.cpython-311.pyc,,
paddleocr/tools/__pycache__/export_center.cpython-311.pyc,,
paddleocr/tools/__pycache__/export_model.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_cls.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_det.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_e2e.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_kie.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_kie_token_ser.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_kie_token_ser_re.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_rec.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_sr.cpython-311.pyc,,
paddleocr/tools/__pycache__/infer_table.cpython-311.pyc,,
paddleocr/tools/__pycache__/naive_sync_bn.cpython-311.pyc,,
paddleocr/tools/__pycache__/program.cpython-311.pyc,,
paddleocr/tools/__pycache__/test_hubserving.cpython-311.pyc,,
paddleocr/tools/__pycache__/train.cpython-311.pyc,,
paddleocr/tools/end2end/__pycache__/convert_ppocr_label.cpython-311.pyc,,
paddleocr/tools/end2end/__pycache__/draw_html.cpython-311.pyc,,
paddleocr/tools/end2end/__pycache__/eval_end2end.cpython-311.pyc,,
paddleocr/tools/end2end/convert_ppocr_label.py,sha256=0ll63ilFKzwsq-Wcj_hfUzYrt3kTvdlv8t119PzI6lM,3262
paddleocr/tools/end2end/draw_html.py,sha256=YAOzhwjaZ5ZwCJpYTXRHKgQ9NFR4ZQIk1UgEvXzrZmY,2198
paddleocr/tools/end2end/eval_end2end.py,sha256=yH9XcHnZxzQRTp_-nFQ66ZbADtwlneuIsiqYYWsZxZU,6636
paddleocr/tools/end2end/readme.md,sha256=G-C2PHPuCyxXSjkbTHv6_205-7jro17vuQqey-GmdE8,2785
paddleocr/tools/eval.py,sha256=ZL02TF4zMSK26P1q3uxMaO9g_Nc7VBzD_aitk4p1W6E,6630
paddleocr/tools/export_center.py,sha256=jwtjub9stTMY2tItBcvCeodEpyCDOeCXnAttp0YgvAE,2653
paddleocr/tools/export_model.py,sha256=JUfjEZ3McJxlKGP4IvOG5MfzlSldbbxiO21TIMPdFWE,1116
paddleocr/tools/infer/__pycache__/predict_cls.cpython-311.pyc,,
paddleocr/tools/infer/__pycache__/predict_det.cpython-311.pyc,,
paddleocr/tools/infer/__pycache__/predict_e2e.cpython-311.pyc,,
paddleocr/tools/infer/__pycache__/predict_rec.cpython-311.pyc,,
paddleocr/tools/infer/__pycache__/predict_sr.cpython-311.pyc,,
paddleocr/tools/infer/__pycache__/predict_system.cpython-311.pyc,,
paddleocr/tools/infer/__pycache__/utility.cpython-311.pyc,,
paddleocr/tools/infer/predict_cls.py,sha256=2suCVUt-sCS_NqqxDn5Mek22kQNFZcpPNhSCTKPY1K8,5782
paddleocr/tools/infer/predict_det.py,sha256=pJJKliFR1yQJqlUfRU4u4Omx3YxhESQrdZNimSwaNck,19368
paddleocr/tools/infer/predict_e2e.py,sha256=lY2ErdsKUalfHbqzsS-eqc4Z4SbMShMlR9wz8oTsrnI,6319
paddleocr/tools/infer/predict_rec.py,sha256=8IYwEMFXb_Hisk9IJIjbkOgpLPD2Mjv_04ApPwyugAY,36805
paddleocr/tools/infer/predict_sr.py,sha256=xE-lj1smv-_gGbSPnOG81XaPDyfMXHNF3Unm7bnwRa0,5764
paddleocr/tools/infer/predict_system.py,sha256=iy30hFgk2TWVuXDrqdCTmC48LtUtju_os8ITEm78Nhw,11478
paddleocr/tools/infer/utility.py,sha256=tECQsD5rtccM0GR2TJvC2TRkzTokcJkrFqvPU5v8KMs,31745
paddleocr/tools/infer_cls.py,sha256=LqfWGff8rrYXoPv0N-RBqKssxv-yHzMbpsuYIfu4Xug,2659
paddleocr/tools/infer_det.py,sha256=0Qx9bdct2-oTf44kQpOy0-4vxbDRMTpMn_QT-1A3XIs,4807
paddleocr/tools/infer_e2e.py,sha256=jOsYIk_iRbcaqNzxZzW2TYRRZ_ckntTF1W9LHXpdK6c,6168
paddleocr/tools/infer_kie.py,sha256=2vDObnBQK0ib1o0LHJtZ44iVFoCbNk2oWsJYCFhYjAw,6311
paddleocr/tools/infer_kie_token_ser.py,sha256=bON3F93nNyHfkCYACsDcvO3psH6kprHn-ec_sCVohIA,5903
paddleocr/tools/infer_kie_token_ser_re.py,sha256=fiCqMv-elGX1sVtzMapaiaGvPpguS2LjITfg8uU72Rg,7864
paddleocr/tools/infer_rec.py,sha256=odIns-8gdIU19B_xbYZG6zn4Kb-14UI0Bo8nE2qlkHA,9261
paddleocr/tools/infer_sr.py,sha256=idaX5nhvd3-kSL-0wWEjXYyJP-6ozw6WFVeG7I1kcbU,3299
paddleocr/tools/infer_table.py,sha256=sg22Ha64kgXwqKwNAJN8Pdq_uaVCz3Bm0yq9Uyvbc2E,4213
paddleocr/tools/naive_sync_bn.py,sha256=ebtl1r0hS3VqPCHTDO4nHRGodod859KTwM_flYFHhQU,4261
paddleocr/tools/program.py,sha256=q1iAxK_7ibRdoch6yucyeuN-B1KxZ4eIjbk_ixwLHKc,33435
paddleocr/tools/test_hubserving.py,sha256=9QwUZc4YxvsJ32yjoYpYBxgKlYLWzpZQ0u0hKBPF95E,5866
paddleocr/tools/train.py,sha256=-ggaEKhaeD-Ui7IzTb3R8mxrBsurLUry4EBAZC5oW8E,10079
