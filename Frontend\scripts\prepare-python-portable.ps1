# 创建便携式Python环境脚本
# 用于将Python环境打包到Electron应用中

param(
    [string]$PythonVersion = "3.11.9",
    [string]$OutputDir = "python-portable"
)

Write-Host "=== 创建便携式Python环境 ===" -ForegroundColor Green

$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$FrontendDir = Split-Path -Parent $ScriptDir
$ProjectRoot = Split-Path -Parent $FrontendDir
$BackendDir = Join-Path $ProjectRoot "Backend_Django"

# 创建输出目录
$PortableDir = Join-Path $FrontendDir $OutputDir
if (Test-Path $PortableDir) {
    Remove-Item $PortableDir -Recurse -Force
}
New-Item -ItemType Directory -Path $PortableDir -Force

try {
    # 步骤1: 下载便携式Python
    Write-Host "步骤1: 下载便携式Python $PythonVersion..." -ForegroundColor Yellow
    
    $PythonUrl = "https://www.python.org/ftp/python/$PythonVersion/python-$PythonVersion-embed-amd64.zip"
    $PythonZip = Join-Path $PortableDir "python-embed.zip"
    
    Write-Host "下载地址: $PythonUrl" -ForegroundColor Cyan
    Invoke-WebRequest -Uri $PythonUrl -OutFile $PythonZip
    
    # 解压Python
    Expand-Archive -Path $PythonZip -DestinationPath $PortableDir -Force
    Remove-Item $PythonZip
    
    Write-Host "✓ Python下载完成" -ForegroundColor Green

    # 步骤2: 配置Python环境
    Write-Host "步骤2: 配置Python环境..." -ForegroundColor Yellow
    
    # 启用pip
    $PthFile = Join-Path $PortableDir "python311._pth"
    if (Test-Path $PthFile) {
        $content = Get-Content $PthFile
        $content = $content -replace "#import site", "import site"
        $content | Set-Content $PthFile
    }
    
    # 下载get-pip.py
    $GetPipUrl = "https://bootstrap.pypa.io/get-pip.py"
    $GetPipPath = Join-Path $PortableDir "get-pip.py"
    Invoke-WebRequest -Uri $GetPipUrl -OutFile $GetPipPath
    
    # 安装pip
    $PythonExe = Join-Path $PortableDir "python.exe"
    & $PythonExe $GetPipPath
    Remove-Item $GetPipPath
    
    Write-Host "✓ pip安装完成" -ForegroundColor Green

    # 步骤3: 安装项目依赖
    Write-Host "步骤3: 安装项目依赖..." -ForegroundColor Yellow
    
    $RequirementsFile = Join-Path $BackendDir "requirements.txt"
    if (Test-Path $RequirementsFile) {
        & $PythonExe -m pip install -r $RequirementsFile --no-warn-script-location
        Write-Host "✓ 项目依赖安装完成" -ForegroundColor Green
    } else {
        Write-Host "⚠ requirements.txt未找到" -ForegroundColor Yellow
    }

    # 步骤4: 清理不必要文件
    Write-Host "步骤4: 清理环境..." -ForegroundColor Yellow
    
    # 删除缓存文件
    $CacheDir = Join-Path $PortableDir "Lib/site-packages/__pycache__"
    if (Test-Path $CacheDir) {
        Remove-Item $CacheDir -Recurse -Force
    }
    
    # 删除测试文件
    Get-ChildItem $PortableDir -Recurse -Name "test*" | ForEach-Object {
        $testPath = Join-Path $PortableDir $_
        if (Test-Path $testPath) {
            Remove-Item $testPath -Recurse -Force
        }
    }
    
    Write-Host "✓ 环境清理完成" -ForegroundColor Green

    # 步骤5: 测试Python环境
    Write-Host "步骤5: 测试Python环境..." -ForegroundColor Yellow
    
    $TestResult = & $PythonExe -c "import sys; print(f'Python {sys.version}'); import django; print(f'Django {django.VERSION}')"
    Write-Host "测试结果: $TestResult" -ForegroundColor Cyan
    
    $EnvSize = (Get-ChildItem $PortableDir -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
    Write-Host "环境大小: $([math]::Round($EnvSize, 2)) MB" -ForegroundColor Cyan

    Write-Host "=== 便携式Python环境创建完成 ===" -ForegroundColor Green
    Write-Host "位置: $PortableDir" -ForegroundColor Cyan
    Write-Host "下一步: 更新electron-builder配置以包含此环境" -ForegroundColor Yellow

} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "便携式Python环境创建失败!" -ForegroundColor Red
    exit 1
}
