# optional, If name is specified it must match the name of the model repository directory containing the model.
name: "det_preprocess"
backend: "python"
max_batch_size: 1

# Input configuration of the model
input [
  {
    # input name
    name: "INPUT_0"
    # input type such as TYPE_FP32、TYPE_UINT8、TYPE_INT8、TYPE_INT16、TYPE_INT32、TYPE_INT64、TYPE_FP16、TYPE_STRING
    data_type: TYPE_UINT8
    # input shape， The batch dimension is omitted and the actual shape is [batch, c, h, w]
    dims: [ -1, -1, 3 ]
  }
]

# The output of the model is configured in the same format as the input
output [
  {
    name: "OUTPUT_0"
    data_type: TYPE_FP32
    dims: [ 3, -1, -1 ]
  },
  {
    name: "OUTPUT_1"
    data_type: TYPE_INT32
    dims: [ 4 ]
  }
]

instance_group [
  {
      count: 1
      kind: KIND_CPU
  }
]
