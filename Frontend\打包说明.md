# AI Vision App - 简单打包说明

## 🎯 目标
打包一个用户拿到就能直接使用的exe文件，无需复杂配置。

## 📋 用户需要的环境
用户电脑上需要安装：
- **Python 3.9 或更高版本** (必须添加到系统PATH)
- **Windows 10 或更高版本**

## 🚀 打包步骤

### 1. 准备环境
```powershell
# 以管理员权限运行PowerShell
# 进入前端目录
cd Frontend

# 清理缓存
Remove-Item -Path "$env:LOCALAPPDATA\electron-builder-cache" -Recurse -Force -ErrorAction SilentlyContinue
npm cache clean --force
```

### 2. 安装依赖
```powershell
npm install
```

### 3. 使用简化配置
```powershell
Copy-Item electron-builder-simple.json electron-builder.json -Force
```

### 4. 构建和打包
```powershell
npm run build
npm run electron:build:win
```

## 📁 打包结果
打包完成后在 `release` 目录下：
- `AI Vision App-1.0.0-x64.exe` - 安装程序
- `win-unpacked/AI Vision App.exe` - 直接运行版本

## 🎯 用户使用方式

### 方式1：安装版本
1. 用户运行 `AI Vision App-1.0.0-x64.exe`
2. 按提示安装
3. 双击桌面快捷方式启动

### 方式2：便携版本
1. 将 `win-unpacked` 文件夹发给用户
2. 用户直接运行 `AI Vision App.exe`

## ⚠️ 重要提醒
- 用户必须先安装Python 3.9+
- Python必须添加到系统PATH
- 首次运行时需要安装Python依赖：
  ```
  pip install -r requirements.txt
  ```

## 🔧 如果用户遇到问题
1. 检查Python是否正确安装：`python --version`
2. 安装依赖：`pip install django ultralytics paddlepaddle onnxruntime opencv-python`
3. 查看应用日志：`%APPDATA%\AI Vision App\app.log`
