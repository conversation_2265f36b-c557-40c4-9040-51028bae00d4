name: "det_postprocess"
backend: "python"
max_batch_size: 128
input [
  {
    name: "POST_INPUT_0"
    data_type: TYPE_FP32
    dims: [ 1, -1, -1]
  },
  {
    name: "POST_INPUT_1"
    data_type: TYPE_INT32
    dims: [ 4 ]
  },
  {
    name: "ORI_IMG"
    data_type: TYPE_UINT8
    dims: [ -1, -1, 3 ]
  }
]

output [
  {
    name: "POST_OUTPUT_0"
    data_type: TYPE_STRING
    dims: [ -1, 1 ]
  },
  {
    name: "POST_OUTPUT_1"
    data_type: TYPE_FP32
    dims: [ -1, 1 ]
  },
  {
    name: "POST_OUTPUT_2"
    data_type: TYPE_FP32
    dims: [ -1, -1, 1 ]
  }
]

instance_group [
  {
      count: 1
      kind: KIND_CPU
  }
]
