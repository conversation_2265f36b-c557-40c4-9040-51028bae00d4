const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { startBackend, stopBackend } = require('./backend.cjs');

// --- 日志记录设置 ---
const userDataPath = app.getPath('userData');
const logFilePath = path.join(userDataPath, 'app.log');

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;
  console.log(message); // 仍然在控制台输出
  try {
    fs.appendFileSync(logFilePath, logMessage);
  } catch (err) {
    console.error('Failed to write to log file:', err);
  }
}

// 清理旧日志 (可选, 应用启动时执行一次)
try {
  if (fs.existsSync(logFilePath)) {
    fs.unlinkSync(logFilePath); 
  }
} catch (err) {
  console.error('Failed to delete old log file:', err);
}

log('Application starting...');
log(`User data path: ${userDataPath}`);
log(`Log file path: ${logFilePath}`);
log(`Node_ENV: ${process.env.NODE_ENV}`);
log(`Resources path: ${process.resourcesPath}`);
// --- 日志记录设置结束 ---

let mainWindow;
let isBackendStarted = false;
let loadingWindow; // 将 loadingWindow 移到这里，以便在 catch 中访问

async function createLoadingWindow() {
  log('Creating loading window...');
  loadingWindow = new BrowserWindow({
    width: 400,
    height: 300,
    frame: false,
    transparent: true,
    resizable: false,
    webPreferences: {
      nodeIntegration: true
    }
  });

  await loadingWindow.loadFile(path.join(__dirname, '../public/loading.html'));
  log('Loading window created and loaded.');
  return loadingWindow;
}

async function createMainWindow() {
  log('Creating main window...');
  mainWindow = new BrowserWindow({
    width: 1280,
    height: 800,
    minWidth: 1024,
    minHeight: 768,
    show: false, // 初始不显示
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.cjs')
    },
    icon: path.join(__dirname, '../public/icon.png')
  });

  // 设置应用菜单
  mainWindow.setMenu(null);
  mainWindow.setTitle('AI Vision Platform');

  // 加载应用
  try {
    if (process.env.NODE_ENV === 'development') {
      log('Loading development URL: http://localhost:5173');
      await mainWindow.loadURL('http://localhost:5173');
      mainWindow.webContents.openDevTools();
    } else {
      // 生产环境文件路径修复
      log(`__dirname: ${__dirname}`);
      log(`process.resourcesPath: ${process.resourcesPath}`);
      log(`app.getAppPath(): ${app.getAppPath()}`);

      // 尝试多个可能的路径
      const possiblePaths = [
        path.join(__dirname, '../dist/index.html'),                    // 开发构建路径
        path.join(app.getAppPath(), 'dist/index.html'),                // asar内部路径
        path.join(process.resourcesPath, 'app.asar/dist/index.html'),  // asar打包路径
        path.join(process.resourcesPath, 'app/dist/index.html'),       // 非asar路径1
        path.join(process.resourcesPath, 'app.asar.unpacked/dist/index.html') // 解包路径
      ];

      let indexPath = null;
      for (const testPath of possiblePaths) {
        log(`Testing path: ${testPath}`);
        if (require('fs').existsSync(testPath)) {
          indexPath = testPath;
          log(`Found index.html at: ${indexPath}`);
          break;
        }
      }

      if (indexPath) {
        await mainWindow.loadFile(indexPath);
      } else {
        // 如果都找不到，列出实际的文件结构
        const appPath = app.getAppPath();
        log(`App path contents:`);
        try {
          const files = require('fs').readdirSync(appPath);
          files.forEach(file => log(`  - ${file}`));
        } catch (e) {
          log(`Cannot read app path: ${e.message}`);
        }

        throw new Error(`Cannot find index.html in any expected location. Searched paths: ${possiblePaths.join(', ')}`);
      }
    }
    log('Main window content loaded.');
    return true;
  } catch (error) {
    log(`Error loading main window content: ${error.stack || error}`);
    dialog.showErrorBox('加载主窗口失败', `无法加载应用内容，请检查日志文件获取详细信息。\n${error.message}`);
    return false;
  }
}

app.whenReady().then(async () => {
  try {
    log('App is ready.');
    loadingWindow = await createLoadingWindow();

    log('Attempting to start backend service...');
    await startBackend();
    isBackendStarted = true;
    log('Backend service started successfully.');

    log('Attempting to create main window...');
    const success = await createMainWindow();
    
    if (success) {
      mainWindow.once('ready-to-show', () => {
        log('Main window is ready to show.');
        mainWindow.show();
        if (loadingWindow) loadingWindow.close();
        log('Main window shown, loading window closed.');
      });

      mainWindow.on('closed', () => {
        log('Main window closed.');
        mainWindow = null;
      });

    } else {
      log('Main window creation failed.');
      if (loadingWindow) loadingWindow.close();
      app.quit();
    }

  } catch (error) {
    log(`Critical error during app startup: ${error.stack || error}`);
    if (loadingWindow) loadingWindow.close();
    dialog.showErrorBox('应用启动失败', `发生严重错误，应用无法启动。请检查日志文件获取详细信息。\n${error.message}`);
    app.quit();
  }
});

app.on('window-all-closed', async () => {
  log('All windows closed.');
  if (isBackendStarted) {
    log('Attempting to stop backend service...');
    await stopBackend();
    log('Backend service stopped.');
  }
  if (process.platform !== 'darwin') {
    log('Quitting application.');
    app.quit();
  }
});

app.on('activate', async () => {
  log('Application activated.');
  if (BrowserWindow.getAllWindows().length === 0) {
    log('No windows open, creating main window.');
    if (isBackendStarted) { // 确保后端已启动，或重新启动
        await createMainWindow();
    } else {
        log('Backend not started, attempting to restart app sequence or show error.');
        // Potentially re-run the startup sequence or show an error
        // For now, just log and quit if backend is essential and not started
        dialog.showErrorBox('错误', '后端服务未成功启动，无法激活窗口。');
        app.quit();
    }
  }
});

// 全局错误处理
process.on('uncaughtException', (error) => {
  log(`Uncaught Exception: ${error.stack || error}`);
  dialog.showErrorBox('发生未捕获的异常', `一个未处理的错误导致应用可能不稳定。请检查日志。\n${error.message}`);
  // 建议在此处安全退出应用，或至少记录下来
  // app.quit(); // 视情况决定是否强制退出
});

process.on('unhandledRejection', (reason, promise) => {
  log(`Unhandled Rejection at: ${promise}, reason: ${reason.stack || reason}`);
  dialog.showErrorBox('发生未处理的Promise拒绝', `一个异步操作失败未被处理。请检查日志。\n${reason.message || reason}`);
  // app.quit(); // 视情况决定是否强制退出
});

// IPC通信处理
ipcMain.handle('get-app-path', () => {
  log('IPC: get-app-path called');
  return app.getAppPath();
});

ipcMain.handle('get-app-version', () => {
  log('IPC: get-app-version called');
  return app.getVersion();
}); 