===========================train_params===========================
model_name:det_res50_db
python:python
gpu_list:0|0,1
trainer.use_gpu:True|True
amp:null
trainer.epochs:lite_train_lite_infer=1|whole_train_whole_infer=300
trainer.output_dir:./output/
dataset.train.loader.batch_size:lite_train_lite_infer=8|whole_train_lite_infer=8
trainer.finetune_checkpoint:null
train_model_name:checkpoint/model_latest.pth
train_infer_img_dir:imgs/paper/db.jpg
null:null
##
trainer:norm_train
norm_train:tools/train.py --config_file config/icdar2015_resnet50_FPN_DBhead_polyLR.yaml -o trainer.log_iter=1 trainer.enable_eval=False dataset.train.loader.shuffle=false arch.backbone.pretrained=False
quant_export:null
fpgm_export:null
distill_train:null
null:null
null:null
##
===========================eval_params===========================
eval:null
null:null
##
===========================infer_params===========================
trainer.output_dir:./output/
trainer.resume_checkpoint:
norm_export:tools/export_model.py --config_file config/icdar2015_resnet50_FPN_DBhead_polyLR.yaml -o
quant_export:null
fpgm_export:null
distill_export:null
export1:null
export2:null
##
train_model:./inference/det_r50_vd_db_v2.0_train/best_accuracy
infer_export:tools/export_model.py --config_file config/icdar2015_resnet50_FPN_DBhead_polyLR.yaml -o
infer_quant:False
inference:tools/infer.py
--use_gpu:True|False
--enable_mkldnn:False
--cpu_threads:6
--batch_size:1
--use_tensorrt:False
--precision:fp32
--model_dir:
--img_path:imgs/paper/db.jpg
--save_log_path:null
--benchmark:True
null:null
===========================infer_benchmark_params==========================
random_infer_input:[{float32,[3,640,640]}];[{float32,[3,960,960]}]
===========================train_benchmark_params==========================
batch_size:8
fp_items:fp32|fp16
epoch:2
--profiler_options:batch_range=[10,20];state=GPU;tracer_option=Default;profile_path=model.profile
flags:FLAGS_eager_delete_tensor_gb=0.0;FLAGS_fraction_of_gpu_memory_to_use=0.98;FLAGS_conv_workspace_size_limit=4096
===========================to_static_train_benchmark_params===========================
to_static_train:trainer.to_static=true
