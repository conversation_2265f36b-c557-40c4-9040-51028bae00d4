Global:
  use_gpu: true
  epoch_num: 1500
  log_smooth_window: 20
  print_batch_step: 20
  save_model_dir: ./output/det_r50_dcn_fce_ctw/
  save_epoch_step: 100
  # evaluation is run every 835 iterations
  eval_batch_step: [0, 835]
  cal_metric_during_train: False
  pretrained_model: ./pretrain_models/ResNet50_vd_ssld_pretrained 
  checkpoints: 
  save_inference_dir: 
  use_visualdl: False
  infer_img: doc/imgs_en/img_10.jpg
  save_res_path: ./output/det_fce/predicts_fce.txt


Architecture:
  model_type: det
  algorithm: FCE
  Transform:
  Backbone:
    name: ResNet_vd
    layers: 50
    dcn_stage: [False, True, True, True]
    out_indices: [1,2,3]
  Neck:
    name: FCEFPN
    out_channels: 256
    has_extra_convs: False
    extra_stage: 0
  Head:
    name: FCEHead
    fourier_degree: 5
Loss:
  name: FCELoss
  fourier_degree: 5
  num_sample: 50
  
Optimizer:
  name: <PERSON>
  beta1: 0.9
  beta2: 0.999
  lr:
    learning_rate: 0.0001
  regularizer:
    name: 'L2'
    factor: 0

PostProcess:
  name: FCEPostProcess
  scales: [8, 16, 32]
  alpha: 1.0
  beta: 1.0
  fourier_degree: 5
  box_type: 'poly'

Metric:
  name: DetFCEMetric
  main_indicator: hmean

Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ctw1500/imgs/
    label_file_list: 
      - ./train_data/ctw1500/imgs/training.txt
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
          ignore_orientation: True
      - DetLabelEncode: # Class handling label
      - ColorJitter: 
          brightness: 0.142
          saturation: 0.5
          contrast: 0.5
      - RandomScaling: 
      - RandomCropFlip:
          crop_ratio: 0.5
      - RandomCropPolyInstances:
          crop_ratio: 0.8
          min_side_ratio: 0.3
      - RandomRotatePolyInstances:
          rotate_ratio: 0.5
          max_angle: 30
          pad_with_fixed_color: False
      - SquareResizePad:
          target_size: 800
          pad_ratio: 0.6
      - IaaAugment:
          augmenter_args:
            - { 'type': Fliplr, 'args': { 'p': 0.5 } }
      - FCENetTargets:
          fourier_degree: 5
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: 'hwc'
      - ToCHWImage:
      - KeepKeys:
          keep_keys: ['image', 'p3_maps', 'p4_maps', 'p5_maps'] # dataloader will return list in this order
  loader:
    shuffle: True
    drop_last: False
    batch_size_per_card: 6
    num_workers: 8

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/ctw1500/imgs/
    label_file_list:
      - ./train_data/ctw1500/imgs/test.txt
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
          ignore_orientation: True
      - DetLabelEncode: # Class handling label
      - DetResizeForTest:
          limit_type: 'min'
          limit_side_len: 736
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: 'hwc'
      - Pad: 
      - ToCHWImage:
      - KeepKeys:
          keep_keys: ['image', 'shape', 'polys', 'ignore_tags']
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 1 # must be 1
    num_workers: 2
