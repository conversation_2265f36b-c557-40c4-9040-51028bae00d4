Global:
  use_gpu: true
  epoch_num: 5
  log_smooth_window: 20
  print_batch_step: 20
  save_model_dir: ./sar_rec
  save_epoch_step: 1
  # evaluation is run every 2000 iterations
  eval_batch_step: [0, 2000]
  cal_metric_during_train: True
  pretrained_model:
  checkpoints: 
  save_inference_dir:
  use_visualdl: False
  infer_img: 
  # for data or label process
  character_dict_path: ppocr/utils/dict90.txt
  max_text_length: 30
  infer_mode: False
  use_space_char: False
  rm_symbol: True
  save_res_path: ./output/rec/predicts_sar.txt

Optimizer:
  name: <PERSON>
  beta1: 0.9
  beta2: 0.999
  lr:
    name: Piecewise
    decay_epochs: [3, 4]
    values: [0.001, 0.0001, 0.00001] 
  regularizer:
    name: 'L2'
    factor: 0

Architecture:
  model_type: rec
  algorithm: SAR
  Transform:
  Backbone:
    name: ResNet31
  Head:
    name: SARHead

Loss:
  name: <PERSON>RLoss

PostProcess:
  name: SARLabelDecode

Metric:
  name: RecMetric


Train:
  dataset:
    name: SimpleDataSet
    label_file_list: ['./train_data/train_list.txt']
    data_dir: ./train_data/
    ratio_list: 1.0
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - SARLabelEncode: # Class handling label
      - SARRecResizeImg:
          image_shape: [3, 48, 48, 160] # h:48 w:[48,160]
          width_downsample_ratio: 0.25
      - KeepKeys:
          keep_keys: ['image', 'label', 'valid_ratio'] # dataloader will return list in this order
  loader:
    shuffle: True
    batch_size_per_card: 64
    drop_last: True
    num_workers: 8
    use_shared_memory: False

Eval:
  dataset:
    name: LMDBDataSet
    data_dir: ./train_data/data_lmdb_release/evaluation/
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - SARLabelEncode: # Class handling label
      - SARRecResizeImg:
          image_shape: [3, 48, 48, 160]
          width_downsample_ratio: 0.25
      - KeepKeys:
          keep_keys: ['image', 'label', 'valid_ratio'] # dataloader will return list in this order
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 64
    num_workers: 4
    use_shared_memory: False
  
