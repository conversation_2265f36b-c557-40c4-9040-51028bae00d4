// Top-level build file where you can add configuration options common to all sub-projects/modules.
//plugins {
//    id 'com.android.application' version '7.2.2' apply false
//    id 'com.android.library' version '7.2.2' apply false
//}
//
//task clean(type: Delete) {
//    delete rootProject.buildDir
//}

buildscript {
    repositories {
        google()
        jcenter()
        // mavenCentral()

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.2'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        // mavenCentral()

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
