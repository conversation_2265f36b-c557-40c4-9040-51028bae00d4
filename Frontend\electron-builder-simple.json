{"appId": "com.aivision.app", "productName": "AI Vision App", "directories": {"output": "release"}, "files": ["dist/**/*", "electron/**/*", "package.json"], "extraResources": [{"from": "../Backend_Django", "to": "Backend_Django", "filter": ["**/*", "!**/__pycache__/**", "!**/*.pyc", "!**/.git/**", "!**/venv/**", "!**/test_*.py", "!**/tests/**", "!**/db.sqlite3"]}], "win": {"icon": "public/favicon.ico", "target": [{"target": "nsis", "arch": ["x64"]}], "sign": false, "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AI Vision App", "runAfterFinish": true}, "compression": "normal", "npmRebuild": false, "nodeGypRebuild": false}