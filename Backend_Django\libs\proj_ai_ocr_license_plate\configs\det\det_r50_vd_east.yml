Global:
  use_gpu: true
  epoch_num: 10000
  log_smooth_window: 20
  print_batch_step: 2
  save_model_dir: ./output/east_r50_vd/
  save_epoch_step: 1000
  # evaluation is run every 5000 iterations after the 4000th iteration
  eval_batch_step: [4000, 5000]
  cal_metric_during_train: False
  pretrained_model: ./pretrain_models/ResNet50_vd_pretrained
  checkpoints: 
  save_inference_dir:
  use_visualdl: False
  infer_img: 
  save_res_path: ./output/det_east/predicts_east.txt

Architecture:
  model_type: det
  algorithm: EAST
  Transform:
  Backbone:
    name: ResNet_vd
    layers: 50
  Neck:
    name: EASTFPN
    model_name: large
  Head:
    name: EASTHead
    model_name: large

Loss:
  name: EASTLoss
  
Optimizer:
  name: Adam
  beta1: 0.9
  beta2: 0.999
  lr:
  #  name: Cosine
    learning_rate: 0.001
  #  warmup_epoch: 0
  regularizer:
    name: 'L2'
    factor: 0

PostProcess:
  name: EASTPostProcess
  score_thresh: 0.8
  cover_thresh: 0.1
  nms_thresh: 0.2

Metric:
  name: DetMetric
  main_indicator: hmean

Train:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/icdar2015/text_localization/
    label_file_list:
      - ./train_data/icdar2015/text_localization/train_icdar2015_label.txt
    ratio_list: [1.0]
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - DetLabelEncode: # Class handling label
      - EASTProcessTrain:
          image_shape: [512, 512]
          background_ratio: 0.125
          min_crop_side_ratio: 0.1
          min_text_size: 10
      - KeepKeys:
          keep_keys: ['image', 'score_map', 'geo_map', 'training_mask'] # dataloader will return list in this order
  loader:
    shuffle: True
    drop_last: False
    batch_size_per_card: 8
    num_workers: 8

Eval:
  dataset:
    name: SimpleDataSet
    data_dir: ./train_data/icdar2015/text_localization/
    label_file_list:
      - ./train_data/icdar2015/text_localization/test_icdar2015_label.txt
    transforms:
      - DecodeImage: # load image
          img_mode: BGR
          channel_first: False
      - DetLabelEncode: # Class handling label
      - DetResizeForTest:
          limit_side_len: 2400
          limit_type: max
      - NormalizeImage:
          scale: 1./255.
          mean: [0.485, 0.456, 0.406]
          std: [0.229, 0.224, 0.225]
          order: 'hwc'
      - ToCHWImage:
      - KeepKeys:
          keep_keys: ['image', 'shape', 'polys', 'ignore_tags']
  loader:
    shuffle: False
    drop_last: False
    batch_size_per_card: 1 # must be 1
    num_workers: 2
